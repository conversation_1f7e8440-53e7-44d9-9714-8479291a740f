import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, Di<PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useQuestionnaires } from '@/context/QuestionnairesContext';
import { useAuth } from '@/context/AuthContext';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { FileQuestion } from 'lucide-react';

interface PatientQuestionnaireAssignmentProps {
  patientId: string;
  onAssignmentComplete?: () => void;
}

export function PatientQuestionnaireAssignment({ patientId, onAssignmentComplete }: PatientQuestionnaireAssignmentProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedQuestionnaireId, setSelectedQuestionnaireId] = useState<string>('');
  const [isAssigning, setIsAssigning] = useState(false);
  const { questionnaires } = useQuestionnaires();
  const { user } = useAuth();

  const handleAssignQuestionnaire = async () => {
    if (!selectedQuestionnaireId || !user) return;

    setIsAssigning(true);
    try {
      const { error } = await supabase
        .from('patient_questionnaire_assignments' as any)
        .insert({
          patient_id: patientId,
          questionnaire_id: selectedQuestionnaireId,
          assigned_by: user.id,
          status: 'pending'
        });

      if (error) {
        throw error;
      }

      toast({
        title: "Sucesso",
        description: "Questionário atribuído ao paciente com sucesso",
      });

      setIsOpen(false);
      setSelectedQuestionnaireId('');
      onAssignmentComplete?.();
    } catch (error) {
      console.error('Erro ao atribuir questionário:', error);
      toast({
        title: "Erro",
        description: "Erro ao atribuir questionário ao paciente",
        variant: "destructive",
      });
    } finally {
      setIsAssigning(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <FileQuestion className="w-4 h-4 mr-2" />
          Atribuir Questionário
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Atribuir Questionário</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <label htmlFor="questionnaire" className="text-sm font-medium">
              Selecione um questionário:
            </label>
            <Select value={selectedQuestionnaireId} onValueChange={setSelectedQuestionnaireId}>
              <SelectTrigger>
                <SelectValue placeholder="Escolha um questionário" />
              </SelectTrigger>
              <SelectContent>
                {questionnaires.map((questionnaire) => (
                  <SelectItem key={questionnaire.id} value={questionnaire.id}>
                    {questionnaire.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancelar
            </Button>
            <Button 
              onClick={handleAssignQuestionnaire}
              disabled={!selectedQuestionnaireId || isAssigning}
            >
              {isAssigning ? 'Atribuindo...' : 'Atribuir'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}