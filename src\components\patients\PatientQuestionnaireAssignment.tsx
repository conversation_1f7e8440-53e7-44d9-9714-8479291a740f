import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useQuestionnaires } from "@/context/QuestionnairesContext";
import { useAssignQuestionnaire } from "@/hooks/usePatientQuestionnaires";
import {
  questionnaireAssignmentSchema,
  type QuestionnaireAssignmentFormData,
} from "@/schemas/questionnaire";
import { FileQuestion } from "lucide-react";

interface PatientQuestionnaireAssignmentProps {
  patientId: string;
  onAssignmentComplete?: () => void;
}

export function PatientQuestionnaireAssignment({
  patientId,
  onAssignmentComplete,
}: PatientQuestionnaireAssignmentProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { questionnaires, loading: questionnairesLoading } =
    useQuestionnaires();
  const assignQuestionnaireMutation = useAssignQuestionnaire();

  const form = useForm<QuestionnaireAssignmentFormData>({
    resolver: zodResolver(questionnaireAssignmentSchema),
    defaultValues: {
      patientId,
      questionnaireId: "",
    },
  });

  const onSubmit = async (data: QuestionnaireAssignmentFormData) => {
    try {
      await assignQuestionnaireMutation.mutateAsync(data);
      setIsOpen(false);
      form.reset();
      onAssignmentComplete?.();
    } catch (error) {
      // Error handling is done in the mutation hook
      console.error("Assignment failed:", error);
    }
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      form.reset();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <FileQuestion className="w-4 h-4 mr-2" />
          Atribuir Questionário
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Atribuir Questionário</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="questionnaireId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Selecione um questionário</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={
                      questionnairesLoading ||
                      assignQuestionnaireMutation.isPending
                    }
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Escolha um questionário" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {questionnaires.map((questionnaire) => (
                        <SelectItem
                          key={questionnaire.id}
                          value={questionnaire.id}
                        >
                          {questionnaire.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={assignQuestionnaireMutation.isPending}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={
                  assignQuestionnaireMutation.isPending || questionnairesLoading
                }
              >
                {assignQuestionnaireMutation.isPending
                  ? "Atribuindo..."
                  : "Atribuir"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
