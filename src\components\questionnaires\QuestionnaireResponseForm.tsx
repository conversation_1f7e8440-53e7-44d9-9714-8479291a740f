import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, ArrowRight, ArrowLeft } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface Question {
  id: string;
  question_text: string;
  question_order: number;
}

interface Questionnaire {
  id: string;
  title: string;
  description?: string;
  questions: Question[];
}

interface QuestionnaireResponseFormProps {
  questionnaire: Questionnaire;
  onComplete: (responses: Array<{ questionId: string; responseText: string }>) => void;
  onCancel: () => void;
  existingResponses?: Array<{ question_id: string; response_text: string }>;
  isReadOnly?: boolean;
}

export function QuestionnaireResponseForm({
  questionnaire,
  onComplete,
  onCancel,
  existingResponses,
  isReadOnly = false
}: QuestionnaireResponseFormProps) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [responses, setResponses] = useState<Record<string, string>>(() => {
    const initial: Record<string, string> = {};
    if (existingResponses) {
      existingResponses.forEach(response => {
        initial[response.question_id] = response.response_text;
      });
    }
    return initial;
  });

  const sortedQuestions = questionnaire.questions.sort((a, b) => a.question_order - b.question_order);
  const currentQuestion = sortedQuestions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / sortedQuestions.length) * 100;

  const handleResponseChange = (questionId: string, value: string) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: value
    }));
  };

  const handleNext = () => {
    if (currentQuestionIndex < sortedQuestions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleComplete = () => {
    const responseArray = sortedQuestions.map(question => ({
      questionId: question.id,
      responseText: responses[question.id] || ''
    }));

    // Verificar se todas as perguntas obrigatórias foram respondidas
    const unansweredQuestions = responseArray.filter(r => !r.responseText.trim());
    if (unansweredQuestions.length > 0) {
      toast({
        title: "Atenção",
        description: `Por favor, responda todas as ${sortedQuestions.length} perguntas antes de enviar.`,
        variant: "destructive",
      });
      return;
    }

    onComplete(responseArray);
  };

  const isLastQuestion = currentQuestionIndex === sortedQuestions.length - 1;
  const isFirstQuestion = currentQuestionIndex === 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">{questionnaire.title}</h2>
          <span className="text-sm text-muted-foreground">
            {currentQuestionIndex + 1} de {sortedQuestions.length}
          </span>
        </div>
        {questionnaire.description && (
          <p className="text-muted-foreground">{questionnaire.description}</p>
        )}
        <Progress value={progress} className="w-full" />
      </div>

      {/* Current Question */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            Pergunta {currentQuestionIndex + 1}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Label className="text-base font-medium leading-relaxed">
            {currentQuestion.question_text}
          </Label>
          <Textarea
            value={responses[currentQuestion.id] || ''}
            onChange={(e) => handleResponseChange(currentQuestion.id, e.target.value)}
            placeholder={isReadOnly ? "Nenhuma resposta fornecida" : "Digite sua resposta aqui..."}
            disabled={isReadOnly}
            className="min-h-[120px] resize-none"
          />
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={isFirstQuestion ? onCancel : handlePrevious}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          {isFirstQuestion ? 'Cancelar' : 'Anterior'}
        </Button>

        <div className="flex items-center gap-2">
          {/* Dots indicator */}
          <div className="flex items-center gap-1">
            {sortedQuestions.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentQuestionIndex(index)}
                disabled={isReadOnly}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentQuestionIndex
                    ? 'bg-primary'
                    : responses[sortedQuestions[index].id]
                    ? 'bg-green-500'
                    : 'bg-muted'
                }`}
              />
            ))}
          </div>
        </div>

        <Button
          onClick={isLastQuestion ? handleComplete : handleNext}
          disabled={isReadOnly}
          className="flex items-center gap-2"
        >
          {isLastQuestion ? (
            <>
              <CheckCircle className="w-4 h-4" />
              {isReadOnly ? 'Fechar' : 'Concluir'}
            </>
          ) : (
            <>
              Próxima
              <ArrowRight className="w-4 h-4" />
            </>
          )}
        </Button>
      </div>

      {/* Summary for last question */}
      {isLastQuestion && !isReadOnly && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-green-700">
              <CheckCircle className="w-5 h-5" />
              <span className="font-medium">Pronto para enviar!</span>
            </div>
            <p className="text-sm text-green-600 mt-1">
              Você respondeu todas as perguntas. Clique em "Concluir" para enviar suas respostas.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}