import { useState } from "react";
import { addDays, subDays, addWeeks, subWeeks } from "date-fns";
import type { ViewMode } from "../types";

export function useDateNavigation() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<ViewMode>("day");

  const navigatePrevious = () => {
    if (viewMode === "week") {
      setCurrentDate(subWeeks(currentDate, 1));
    } else {
      setCurrentDate(subDays(currentDate, 1));
    }
  };

  const navigateNext = () => {
    if (viewMode === "week") {
      setCurrentDate(addWeeks(currentDate, 1));
    } else {
      setCurrentDate(addDays(currentDate, 1));
    }
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  return {
    currentDate,
    viewMode,
    setViewMode,
    navigatePrevious,
    navigateNext,
    goToToday,
  };
}
