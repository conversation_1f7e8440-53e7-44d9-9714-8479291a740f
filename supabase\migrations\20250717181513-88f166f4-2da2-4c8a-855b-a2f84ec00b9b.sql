-- Create patients table
CREATE TABLE public.patients (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  cpf TEXT NOT NULL,
  phone TEXT NOT NULL,
  main_complaints TEXT,
  anamnesis TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create consultation summaries table
CREATE TABLE public.consultation_summaries (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  patient_id UUID NOT NULL REFERENCES public.patients(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  summary TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.consultation_summaries ENABLE ROW LEVEL SECURITY;

-- Create policies for patients table
CREATE POLICY "Users can view their own patients" 
ON public.patients 
FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own patients" 
ON public.patients 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own patients" 
ON public.patients 
FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own patients" 
ON public.patients 
FOR DELETE 
USING (auth.uid() = user_id);

-- Create policies for consultation summaries table
CREATE POLICY "Users can view consultations for their own patients" 
ON public.consultation_summaries 
FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.patients 
    WHERE patients.id = consultation_summaries.patient_id 
    AND patients.user_id = auth.uid()
  )
);

CREATE POLICY "Users can create consultations for their own patients" 
ON public.consultation_summaries 
FOR INSERT 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.patients 
    WHERE patients.id = consultation_summaries.patient_id 
    AND patients.user_id = auth.uid()
  )
);

CREATE POLICY "Users can update consultations for their own patients" 
ON public.consultation_summaries 
FOR UPDATE 
USING (
  EXISTS (
    SELECT 1 FROM public.patients 
    WHERE patients.id = consultation_summaries.patient_id 
    AND patients.user_id = auth.uid()
  )
);

CREATE POLICY "Users can delete consultations for their own patients" 
ON public.consultation_summaries 
FOR DELETE 
USING (
  EXISTS (
    SELECT 1 FROM public.patients 
    WHERE patients.id = consultation_summaries.patient_id 
    AND patients.user_id = auth.uid()
  )
);

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_patients_updated_at
BEFORE UPDATE ON public.patients
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_consultation_summaries_updated_at
BEFORE UPDATE ON public.consultation_summaries
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();