import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './AuthContext';
import { toast } from '@/hooks/use-toast';

export interface Question {
  id: string;
  question_text: string;
  question_order: number;
}

export interface Questionnaire {
  id: string;
  title: string;
  description?: string;
  created_at: string;
  updated_at: string;
  questions?: Question[];
}

interface QuestionnairesContextType {
  questionnaires: Questionnaire[];
  loading: boolean;
  addQuestionnaire: (questionnaire: Omit<Questionnaire, 'id' | 'created_at' | 'updated_at'>) => Promise<Questionnaire | null>;
  updateQuestionnaire: (id: string, questionnaire: Partial<Questionnaire>) => Promise<void>;
  deleteQuestionnaire: (id: string) => Promise<void>;
  addQuestion: (questionnaireId: string, questionText: string, order: number) => Promise<void>;
  updateQuestion: (questionId: string, questionText: string) => Promise<void>;
  deleteQuestion: (questionId: string) => Promise<void>;
  fetchQuestionnaires: () => Promise<void>;
}

const QuestionnairesContext = createContext<QuestionnairesContextType | null>(null);

export const useQuestionnaires = () => {
  const context = useContext(QuestionnairesContext);
  if (!context) {
    throw new Error('useQuestionnaires must be used within a QuestionnairesProvider');
  }
  return context;
};

export const QuestionnairesProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [questionnaires, setQuestionnaires] = useState<Questionnaire[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  const fetchQuestionnaires = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data: questionnairesData, error } = await supabase
        .from('questionnaires')
        .select(`
          *,
          questionnaire_questions (
            id,
            question_text,
            question_order
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const formattedQuestionnaires = questionnairesData?.map(q => ({
        ...q,
        questions: q.questionnaire_questions?.sort((a, b) => a.question_order - b.question_order) || []
      })) || [];

      setQuestionnaires(formattedQuestionnaires);
    } catch (error) {
      console.error('Erro ao buscar questionários:', error);
      toast({
        title: "Erro",
        description: "Erro ao carregar questionários",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const addQuestionnaire = async (questionnaire: Omit<Questionnaire, 'id' | 'created_at' | 'updated_at'>) => {
    if (!user) return null;

    try {
      const { data, error } = await supabase
        .from('questionnaires')
        .insert([{
          ...questionnaire,
          user_id: user.id
        }])
        .select()
        .single();

      if (error) throw error;

      const newQuestionnaire = { ...data, questions: [] };
      setQuestionnaires(prev => [newQuestionnaire, ...prev]);
      toast({
        title: "Sucesso",
        description: "Questionário criado com sucesso",
      });
      return newQuestionnaire;
    } catch (error) {
      console.error('Erro ao criar questionário:', error);
      toast({
        title: "Erro",
        description: "Erro ao criar questionário",
        variant: "destructive",
      });
      return null;
    }
  };

  const updateQuestionnaire = async (id: string, questionnaire: Partial<Questionnaire>) => {
    try {
      const { error } = await supabase
        .from('questionnaires')
        .update(questionnaire)
        .eq('id', id);

      if (error) throw error;

      setQuestionnaires(prev => prev.map(q => 
        q.id === id ? { ...q, ...questionnaire } : q
      ));
      toast({
        title: "Sucesso",
        description: "Questionário atualizado com sucesso",
      });
    } catch (error) {
      console.error('Erro ao atualizar questionário:', error);
      toast({
        title: "Erro",
        description: "Erro ao atualizar questionário",
        variant: "destructive",
      });
    }
  };

  const deleteQuestionnaire = async (id: string) => {
    try {
      const { error } = await supabase
        .from('questionnaires')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setQuestionnaires(prev => prev.filter(q => q.id !== id));
      toast({
        title: "Sucesso",
        description: "Questionário excluído com sucesso",
      });
    } catch (error) {
      console.error('Erro ao excluir questionário:', error);
      toast({
        title: "Erro",
        description: "Erro ao excluir questionário",
        variant: "destructive",
      });
    }
  };

  const addQuestion = async (questionnaireId: string, questionText: string, order: number) => {
    try {
      const { data, error } = await supabase
        .from('questionnaire_questions')
        .insert([{
          questionnaire_id: questionnaireId,
          question_text: questionText,
          question_order: order
        }])
        .select()
        .single();

      if (error) throw error;

      setQuestionnaires(prev => prev.map(q => 
        q.id === questionnaireId 
          ? { ...q, questions: [...(q.questions || []), data].sort((a, b) => a.question_order - b.question_order) }
          : q
      ));
    } catch (error) {
      console.error('Erro ao adicionar pergunta:', error);
      toast({
        title: "Erro",
        description: "Erro ao adicionar pergunta",
        variant: "destructive",
      });
    }
  };

  const updateQuestion = async (questionId: string, questionText: string) => {
    try {
      const { error } = await supabase
        .from('questionnaire_questions')
        .update({ question_text: questionText })
        .eq('id', questionId);

      if (error) throw error;

      setQuestionnaires(prev => prev.map(q => ({
        ...q,
        questions: q.questions?.map(question => 
          question.id === questionId 
            ? { ...question, question_text: questionText }
            : question
        )
      })));
    } catch (error) {
      console.error('Erro ao atualizar pergunta:', error);
      toast({
        title: "Erro",
        description: "Erro ao atualizar pergunta",
        variant: "destructive",
      });
    }
  };

  const deleteQuestion = async (questionId: string) => {
    try {
      const { error } = await supabase
        .from('questionnaire_questions')
        .delete()
        .eq('id', questionId);

      if (error) throw error;

      setQuestionnaires(prev => prev.map(q => ({
        ...q,
        questions: q.questions?.filter(question => question.id !== questionId)
      })));
    } catch (error) {
      console.error('Erro ao excluir pergunta:', error);
      toast({
        title: "Erro",
        description: "Erro ao excluir pergunta",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (user) {
      fetchQuestionnaires();
    }
  }, [user]);

  return (
    <QuestionnairesContext.Provider value={{
      questionnaires,
      loading,
      addQuestionnaire,
      updateQuestionnaire,
      deleteQuestionnaire,
      addQuestion,
      updateQuestion,
      deleteQuestion,
      fetchQuestionnaires
    }}>
      {children}
    </QuestionnairesContext.Provider>
  );
};