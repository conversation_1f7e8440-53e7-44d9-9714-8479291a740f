import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { User, Session } from '@supabase/supabase-js';
import type { Database } from '@/integrations/supabase/types';

type Profile = Database['public']['Tables']['profiles']['Row'];

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  session: Session | null;
  profile: Profile | null;
  clinicInfo: {
    doctorName: string;
    clinicName: string;
    crp: string;
    phone: string;
    email: string;
  };
  login: (email: string, password: string) => Promise<{ error?: string }>;
  signUp: (email: string, password: string) => Promise<{ error?: string }>;
  logout: () => Promise<void>;
  updateClinicInfo: (info: Partial<AuthContextType['clinicInfo']>) => Promise<{ error?: string }>;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [clinicInfo, setClinicInfo] = useState({
    doctorName: 'Dr. João Silva',
    clinicName: 'PsicoSys',
    crp: 'CRP 06/12345',
    phone: '(11) 99999-9999',
    email: '<EMAIL>'
  });

  useEffect(() => {
    // Set up auth state listener FIRST
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
        
        // Load clinic info from profiles table when user logs in
        if (session?.user) {
          setTimeout(() => {
            loadProfile(session.user.id);
          }, 0);
        } else {
          setProfile(null);
        }
      }
    );

    // THEN check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
      
      if (session?.user) {
        loadProfile(session.user.id);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const loadProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .maybeSingle();

      if (error) {
        console.error('Error loading profile:', error);
        return;
      }

      if (data) {
        setProfile(data);
        setClinicInfo({
          doctorName: data.doctor_name,
          clinicName: data.clinic_name,
          crp: data.crp,
          phone: data.phone,
          email: data.email,
        });
      }
    } catch (error) {
      console.error('Error loading profile:', error);
    }
  };

  const login = async (email: string, password: string): Promise<{ error?: string }> => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        return { error: error.message };
      }

      return {};
    } catch (error) {
      return { error: 'Erro inesperado ao fazer login' };
    }
  };

  const signUp = async (email: string, password: string): Promise<{ error?: string }> => {
    try {
      const redirectUrl = `${window.location.origin}/`;
      
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: redirectUrl
        }
      });

      if (error) {
        return { error: error.message };
      }

      return {};
    } catch (error) {
      return { error: 'Erro inesperado ao criar conta' };
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await supabase.auth.signOut();
      setClinicInfo({
        doctorName: 'Dr. João Silva',
        clinicName: 'PsicoSys',
        crp: 'CRP 06/12345',
        phone: '(11) 99999-9999',
        email: '<EMAIL>'
      });
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const updateClinicInfo = async (info: Partial<AuthContextType['clinicInfo']>): Promise<{ error?: string }> => {
    if (!user) {
      return { error: 'Usuário não autenticado' };
    }

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          doctor_name: info.doctorName,
          clinic_name: info.clinicName,
          crp: info.crp,
          phone: info.phone,
          email: info.email,
        })
        .eq('user_id', user.id);

      if (error) {
        console.error('Error updating profile:', error);
        return { error: 'Erro ao atualizar perfil' };
      }

      // Update local state
      setClinicInfo(prev => ({
        ...prev,
        ...info
      }));

      // Reload profile to sync with database
      await loadProfile(user.id);

      return {};
    } catch (error) {
      console.error('Error updating profile:', error);
      return { error: 'Erro ao atualizar perfil' };
    }
  };

  return (
    <AuthContext.Provider value={{
      isAuthenticated: !!session,
      user,
      session,
      profile,
      clinicInfo,
      login,
      signUp,
      logout,
      updateClinicInfo,
      loading
    }}>
      {children}
    </AuthContext.Provider>
  );
};