import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useAppointments } from "@/context/AppointmentsContext";
import { useAppointmentForm } from "./hooks/useAppointmentForm";
import { useDateNavigation } from "./hooks/useDateNavigation";
import { AppointmentFormDialog } from "./components/AppointmentFormDialog";
import { DateNavigation } from "./components/DateNavigation";
import { DayView } from "./components/DayView";
import { WeekView } from "./components/WeekView";

export default function Agenda() {
  const { appointments, loading } = useAppointments();
  const {
    formData,
    setFormData,
    showAddDialog,
    editingAppointment,
    handleSubmit,
    handleEdit,
    handleDelete,
    openAddDialog,
    closeDialog,
  } = useAppointmentForm();

  const {
    currentDate,
    viewMode,
    setViewMode,
    navigatePrevious,
    navigateNext,
    goToToday,
  } = useDateNavigation();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Agenda</h1>
          <p className="text-muted-foreground">
            Gerencie seus agendamentos e consultas
          </p>
        </div>
        <Button
          className="bg-primary hover:bg-primary-hover"
          onClick={openAddDialog}
        >
          <Plus className="w-4 h-4 mr-2" />
          Novo Agendamento
        </Button>
      </div>

      <DateNavigation
        currentDate={currentDate}
        viewMode={viewMode}
        setViewMode={setViewMode}
        onNavigatePrevious={navigatePrevious}
        onNavigateNext={navigateNext}
        onGoToToday={goToToday}
      />

      {loading ? (
        <div className="text-center py-8">
          <p className="text-muted-foreground">Carregando agendamentos...</p>
        </div>
      ) : viewMode === "week" ? (
        <WeekView
          currentDate={currentDate}
          appointments={appointments}
          onEdit={handleEdit}
        />
      ) : (
        <DayView
          currentDate={currentDate}
          appointments={appointments}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      )}

      <AppointmentFormDialog
        open={showAddDialog}
        onOpenChange={closeDialog}
        editingAppointment={editingAppointment}
        formData={formData}
        setFormData={setFormData}
        onSubmit={handleSubmit}
      />
    </div>
  );
}
