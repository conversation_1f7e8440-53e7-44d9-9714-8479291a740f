# 🔧 Configuração das Tabelas de Questionários

## ❌ Problema Identificado

O erro que você está enfrentando:

```
{
    "code": "PGRST200",
    "details": "Searched for a foreign key relationship between 'patient_questionnaire_assignments' and 'questionnaires' in the schema 'public', but no matches were found.",
    "hint": "Perhaps you meant 'questionnaire_questions' instead of 'patient_questionnaire_assignments'.",
    "message": "Could not find a relationship between 'patient_questionnaire_assignments' and 'questionnaires' in the schema cache"
}
```

**Causa:** As tabelas `patient_questionnaire_assignments` e `patient_questionnaire_responses` não existem no banco de dados remoto do Supabase.

## ✅ Solução

### Opção 1: Executar Script SQL no Supabase (RECOMENDADO)

1. **Acesse o Supabase Dashboard:**
   - Vá para [https://supabase.com/dashboard](https://supabase.com/dashboard)
   - Faça login na sua conta
   - Selecione o projeto `mzyrgkihwhkkrnutuazh`

2. **Abra o SQL Editor:**
   - No menu lateral, clique em "SQL Editor"
   - Clique em "New query"

3. **Execute o Script:**
   - Copie todo o conteúdo do arquivo `database-setup.sql`
   - Cole no editor SQL
   - Clique em "Run" para executar

4. **Verifique a Criação:**
   - Execute esta query para verificar:
   ```sql
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name LIKE '%questionnaire%';
   ```

### Opção 2: Aplicar Migração via CLI (Alternativa)

Se você tiver o Supabase CLI configurado:

```bash
# 1. Fazer login no Supabase
npx supabase login

# 2. Linkar o projeto
npx supabase link --project-ref mzyrgkihwhkkrnutuazh

# 3. Aplicar migrações
npx supabase db push
```

## 🔄 Código Atualizado

O código foi **automaticamente atualizado** para funcionar mesmo sem as relações de chave estrangeira:

### ✅ Mudanças Implementadas:

1. **Queries Manuais:** As funções HTTP agora fazem queries separadas e combinam os dados manualmente
2. **Tratamento de Erros:** Código robusto que lida com tabelas inexistentes
3. **Fallback Gracioso:** Se as tabelas não existirem, retorna arrays vazios

### 📁 Arquivos Atualizados:

- `src/http/patient-questionnaires.ts` - Queries centralizadas e robustas
- `src/hooks/usePatientQuestionnaires.ts` - Hooks simplificados usando as funções HTTP

## 🚀 Após Executar o Script

Depois de executar o script SQL no Supabase:

1. **Teste a Aplicação:**
   - Recarregue a página da aplicação
   - Tente atribuir um questionário a um paciente
   - Verifique se não há mais erros no console

2. **Funcionalidades Disponíveis:**
   - ✅ Atribuir questionários a pacientes
   - ✅ Visualizar questionários atribuídos
   - ✅ Responder questionários
   - ✅ Visualizar respostas
   - ✅ Atualizar status dos questionários

## 📊 Estrutura das Tabelas Criadas

### `patient_questionnaire_assignments`
- Armazena as atribuições de questionários aos pacientes
- Relaciona pacientes com questionários
- Controla status (pending/completed)

### `patient_questionnaire_responses`
- Armazena as respostas dos questionários
- Relaciona com as atribuições e questões
- Evita respostas duplicadas

## 🔒 Segurança (RLS)

O script inclui políticas de Row Level Security que garantem:
- Usuários só veem seus próprios dados
- Proteção contra acesso não autorizado
- Isolamento de dados por usuário

## 🆘 Se Ainda Houver Problemas

1. **Verifique as Tabelas:**
   ```sql
   \dt *questionnaire*
   ```

2. **Verifique as Políticas RLS:**
   ```sql
   SELECT * FROM pg_policies WHERE tablename LIKE '%questionnaire%';
   ```

3. **Console do Navegador:**
   - Abra as ferramentas de desenvolvedor (F12)
   - Verifique se há erros no console
   - Procure por mensagens de erro específicas

## 📞 Suporte

Se precisar de ajuda adicional, forneça:
- Screenshot do erro
- Logs do console do navegador
- Resultado da query de verificação das tabelas
