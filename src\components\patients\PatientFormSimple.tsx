import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ArrowRight } from "lucide-react";

interface PatientFormData {
  name: string;
  cpf: string;
  phone: string;
  mainComplaints: string;
  anamnesis: string;
}

interface PatientFormSimpleProps {
  onSubmit: (data: PatientFormData) => void;
}

export function PatientFormSimple({ onSubmit }: PatientFormSimpleProps) {
  const [formData, setFormData] = useState<PatientFormData>({
    name: "",
    cpf: "",
    phone: "",
    mainComplaints: "",
    anamnesis: "",
  });

  const formatCPF = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    return numbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  };

  const formatPhone = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    if (numbers.length <= 10) {
      return numbers.replace(/(\d{2})(\d{4})(\d{0,4})/, '($1) $2-$3');
    }
    return numbers.replace(/(\d{2})(\d{5})(\d{0,4})/, '($1) $2-$3');
  };

  const handleInputChange = (field: keyof PatientFormData, value: string) => {
    let formattedValue = value;
    
    if (field === 'cpf') {
      formattedValue = formatCPF(value);
    } else if (field === 'phone') {
      formattedValue = formatPhone(value);
    }
    
    setFormData(prev => ({
      ...prev,
      [field]: formattedValue
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Nome Completo</Label>
          <Input
            id="name"
            type="text"
            placeholder="Digite o nome completo"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="cpf">CPF</Label>
          <Input
            id="cpf"
            type="text"
            placeholder="000.000.000-00"
            value={formData.cpf}
            onChange={(e) => handleInputChange('cpf', e.target.value)}
            maxLength={14}
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="phone">Telefone</Label>
        <Input
          id="phone"
          type="tel"
          placeholder="(00) 00000-0000"
          value={formData.phone}
          onChange={(e) => handleInputChange('phone', e.target.value)}
          maxLength={15}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="mainComplaints">Queixas Principais</Label>
        <Textarea
          id="mainComplaints"
          placeholder="Descreva as principais queixas do paciente..."
          value={formData.mainComplaints}
          onChange={(e) => handleInputChange('mainComplaints', e.target.value)}
          className="min-h-[100px]"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="anamnesis">Anamnese</Label>
        <Textarea
          id="anamnesis"
          placeholder="Histórico médico e informações relevantes..."
          value={formData.anamnesis}
          onChange={(e) => handleInputChange('anamnesis', e.target.value)}
          className="min-h-[120px]"
        />
      </div>

      <Button type="submit" className="w-full">
        Próximo
        <ArrowRight className="w-4 h-4 ml-2" />
      </Button>
    </form>
  );
}