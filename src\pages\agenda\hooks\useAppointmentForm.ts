import { useState } from "react";
import { format } from "date-fns";
import { useAppointments } from "@/context/AppointmentsContext";
import { useToast } from "@/hooks/use-toast";
import type { Appointment } from "@/context/AppointmentsContext";
import type { AppointmentFormData } from "../types";

export function useAppointmentForm() {
  const { addAppointment, updateAppointment, deleteAppointment } =
    useAppointments();
  const { toast } = useToast();

  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingAppointment, setEditingAppointment] =
    useState<Appointment | null>(null);

  const [formData, setFormData] = useState<AppointmentFormData>({
    title: "",
    description: "",
    patient_id: "",
    appointment_date: format(new Date(), "yyyy-MM-dd"),
    start_time: "09:00",
    end_time: "10:00",
    status: "scheduled",
  });

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      patient_id: "",
      appointment_date: format(new Date(), "yyyy-MM-dd"),
      start_time: "09:00",
      end_time: "10:00",
      status: "scheduled",
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim()) {
      toast({
        title: "Erro",
        description: "Título é obrigatório",
        variant: "destructive",
      });
      return;
    }

    try {
      if (editingAppointment) {
        await updateAppointment(editingAppointment.id, formData);
        toast({
          title: "Agendamento atualizado",
          description: "O agendamento foi atualizado com sucesso.",
        });
      } else {
        await addAppointment(formData);
        toast({
          title: "Agendamento criado",
          description: "Novo agendamento foi adicionado com sucesso.",
        });
      }

      setShowAddDialog(false);
      setEditingAppointment(null);
      resetForm();
    } catch (error) {
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao salvar o agendamento.",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (appointment: Appointment) => {
    setEditingAppointment(appointment);
    setFormData({
      title: appointment.title,
      description: appointment.description || "",
      patient_id: appointment.patient_id || "",
      appointment_date: appointment.appointment_date,
      start_time: appointment.start_time,
      end_time: appointment.end_time,
      status: appointment.status,
    });
    setShowAddDialog(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteAppointment(id);
      toast({
        title: "Agendamento excluído",
        description: "O agendamento foi excluído com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao excluir o agendamento.",
        variant: "destructive",
      });
    }
  };

  const openAddDialog = () => {
    resetForm();
    setEditingAppointment(null);
    setShowAddDialog(true);
  };

  const closeDialog = () => {
    setShowAddDialog(false);
    setEditingAppointment(null);
    resetForm();
  };

  return {
    formData,
    setFormData,
    showAddDialog,
    editingAppointment,
    handleSubmit,
    handleEdit,
    handleDelete,
    openAddDialog,
    closeDialog,
    resetForm,
  };
}
