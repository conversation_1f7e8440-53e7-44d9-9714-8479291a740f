import { useState } from "react";
import { useMyQuestionnaireAssignments } from "@/hooks/usePatientQuestionnaireView";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PatientQuestionnaireForm } from "@/components/patient/PatientQuestionnaireForm";
import { PatientQuestionnaireResponses } from "@/components/patient/PatientQuestionnaireResponses";
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Calendar,
  User
} from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import type { AssignmentWithQuestionnaire } from "@/schemas/questionnaire";

type ViewMode = "list" | "answer" | "view-responses";

interface PatientQuestionnairesState {
  mode: ViewMode;
  selectedAssignment: AssignmentWithQuestionnaire | null;
}

export default function PatientQuestionnaires() {
  const { data: assignments, isLoading, error } = useMyQuestionnaireAssignments();
  const [state, setState] = useState<PatientQuestionnairesState>({
    mode: "list",
    selectedAssignment: null,
  });

  const handleAnswerQuestionnaire = (assignment: AssignmentWithQuestionnaire) => {
    setState({
      mode: "answer",
      selectedAssignment: assignment,
    });
  };

  const handleViewResponses = (assignment: AssignmentWithQuestionnaire) => {
    setState({
      mode: "view-responses",
      selectedAssignment: assignment,
    });
  };

  const handleBackToList = () => {
    setState({
      mode: "list",
      selectedAssignment: null,
    });
  };

  const handleQuestionnaireCompleted = () => {
    setState({
      mode: "list",
      selectedAssignment: null,
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Meus Questionários</h1>
            <p className="text-muted-foreground">
              Visualize e responda seus questionários atribuídos
            </p>
          </div>
        </div>

        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Meus Questionários</h1>
            <p className="text-muted-foreground">
              Visualize e responda seus questionários atribuídos
            </p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Erro ao carregar questionários. Tente recarregar a página.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Render different views based on current mode
  if (state.mode === "answer" && state.selectedAssignment) {
    return (
      <PatientQuestionnaireForm
        assignment={state.selectedAssignment}
        onBack={handleBackToList}
        onCompleted={handleQuestionnaireCompleted}
      />
    );
  }

  if (state.mode === "view-responses" && state.selectedAssignment) {
    return (
      <PatientQuestionnaireResponses
        assignment={state.selectedAssignment}
        onBack={handleBackToList}
      />
    );
  }

  // Main list view
  const pendingAssignments = assignments?.filter(a => a.status === "pending") || [];
  const completedAssignments = assignments?.filter(a => a.status === "completed") || [];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Meus Questionários</h1>
          <p className="text-muted-foreground">
            Visualize e responda seus questionários atribuídos
          </p>
        </div>
        <div className="flex gap-2">
          <Badge variant="outline" className="text-orange-600 border-orange-200">
            <Clock className="w-3 h-3 mr-1" />
            {pendingAssignments.length} Pendente{pendingAssignments.length !== 1 ? 's' : ''}
          </Badge>
          <Badge variant="outline" className="text-green-600 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            {completedAssignments.length} Concluído{completedAssignments.length !== 1 ? 's' : ''}
          </Badge>
        </div>
      </div>

      {!assignments || assignments.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Nenhum questionário encontrado</h3>
            <p className="text-muted-foreground text-center">
              Você não possui questionários atribuídos no momento.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {/* Pending Questionnaires */}
          {pendingAssignments.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Clock className="w-5 h-5 mr-2 text-orange-600" />
                Questionários Pendentes
              </h2>
              <div className="grid gap-4">
                {pendingAssignments.map((assignment) => (
                  <QuestionnaireCard
                    key={assignment.id}
                    assignment={assignment}
                    onAnswer={handleAnswerQuestionnaire}
                    onViewResponses={handleViewResponses}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Completed Questionnaires */}
          {completedAssignments.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <CheckCircle className="w-5 h-5 mr-2 text-green-600" />
                Questionários Concluídos
              </h2>
              <div className="grid gap-4">
                {completedAssignments.map((assignment) => (
                  <QuestionnaireCard
                    key={assignment.id}
                    assignment={assignment}
                    onAnswer={handleAnswerQuestionnaire}
                    onViewResponses={handleViewResponses}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

interface QuestionnaireCardProps {
  assignment: AssignmentWithQuestionnaire;
  onAnswer: (assignment: AssignmentWithQuestionnaire) => void;
  onViewResponses: (assignment: AssignmentWithQuestionnaire) => void;
}

function QuestionnaireCard({ assignment, onAnswer, onViewResponses }: QuestionnaireCardProps) {
  const isPending = assignment.status === "pending";
  const questionnaire = assignment.questionnaire;

  if (!questionnaire) {
    return null;
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg">{questionnaire.title}</CardTitle>
            <CardDescription>{questionnaire.description}</CardDescription>
          </div>
          <Badge variant={isPending ? "default" : "secondary"}>
            {isPending ? "Pendente" : "Concluído"}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center text-sm text-muted-foreground">
            <FileText className="w-4 h-4 mr-2" />
            {questionnaire.questions?.length || 0} pergunta{(questionnaire.questions?.length || 0) !== 1 ? 's' : ''}
          </div>
          
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="w-4 h-4 mr-2" />
            Atribuído em {format(new Date(assignment.assigned_at), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
          </div>

          {assignment.completed_at && (
            <div className="flex items-center text-sm text-muted-foreground">
              <CheckCircle className="w-4 h-4 mr-2" />
              Concluído em {format(new Date(assignment.completed_at), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
            </div>
          )}

          <div className="flex gap-2 pt-2">
            {isPending ? (
              <Button onClick={() => onAnswer(assignment)} className="flex-1">
                <FileText className="w-4 h-4 mr-2" />
                Responder Questionário
              </Button>
            ) : (
              <Button 
                variant="outline" 
                onClick={() => onViewResponses(assignment)}
                className="flex-1"
              >
                <User className="w-4 h-4 mr-2" />
                Ver Minhas Respostas
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
