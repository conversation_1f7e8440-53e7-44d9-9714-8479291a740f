-- =====================================================
-- SCRIPT PARA CRIAR TABELAS DE QUESTIONÁRIOS
-- Execute este script no SQL Editor do Supabase
-- =====================================================

-- 1. Criar tabela de atribuições de questionários
CREATE TABLE IF NOT EXISTS patient_questionnaire_assignments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  patient_id UUID NOT NULL REFERENCES patients(id) ON DELETE CASCADE,
  questionnaire_id UUID NOT NULL REFERENCES questionnaires(id) ON DELETE CASCADE,
  assigned_by UUID NOT NULL REFERENCES auth.users(id),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed')),
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Criar tabela de respostas dos questionários
CREATE TABLE IF NOT EXISTS patient_questionnaire_responses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  assignment_id UUID NOT NULL REFERENCES patient_questionnaire_assignments(id) ON DELETE CASCADE,
  question_id UUID NOT NULL REFERENCES questionnaire_questions(id) ON DELETE CASCADE,
  response_text TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_patient_questionnaire_assignments_patient_id 
ON patient_questionnaire_assignments(patient_id);

CREATE INDEX IF NOT EXISTS idx_patient_questionnaire_assignments_questionnaire_id 
ON patient_questionnaire_assignments(questionnaire_id);

CREATE INDEX IF NOT EXISTS idx_patient_questionnaire_assignments_assigned_by 
ON patient_questionnaire_assignments(assigned_by);

CREATE INDEX IF NOT EXISTS idx_patient_questionnaire_assignments_status 
ON patient_questionnaire_assignments(status);

CREATE INDEX IF NOT EXISTS idx_patient_questionnaire_responses_assignment_id 
ON patient_questionnaire_responses(assignment_id);

CREATE INDEX IF NOT EXISTS idx_patient_questionnaire_responses_question_id 
ON patient_questionnaire_responses(question_id);

-- 4. Criar constraint única para evitar respostas duplicadas
ALTER TABLE patient_questionnaire_responses 
ADD CONSTRAINT unique_assignment_question 
UNIQUE (assignment_id, question_id);

-- 5. Criar trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Aplicar trigger nas tabelas
CREATE TRIGGER update_patient_questionnaire_assignments_updated_at 
BEFORE UPDATE ON patient_questionnaire_assignments 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_patient_questionnaire_responses_updated_at 
BEFORE UPDATE ON patient_questionnaire_responses 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 6. Configurar Row Level Security (RLS)
ALTER TABLE patient_questionnaire_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE patient_questionnaire_responses ENABLE ROW LEVEL SECURITY;

-- 7. Criar políticas RLS para patient_questionnaire_assignments
CREATE POLICY "Users can view their own patient questionnaire assignments" 
ON patient_questionnaire_assignments FOR SELECT 
USING (
  assigned_by = auth.uid() OR 
  patient_id IN (
    SELECT id FROM patients WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can insert questionnaire assignments for their patients" 
ON patient_questionnaire_assignments FOR INSERT 
WITH CHECK (
  assigned_by = auth.uid() AND 
  patient_id IN (
    SELECT id FROM patients WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can update their own questionnaire assignments" 
ON patient_questionnaire_assignments FOR UPDATE 
USING (
  assigned_by = auth.uid() OR 
  patient_id IN (
    SELECT id FROM patients WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can delete their own questionnaire assignments" 
ON patient_questionnaire_assignments FOR DELETE 
USING (
  assigned_by = auth.uid() OR 
  patient_id IN (
    SELECT id FROM patients WHERE user_id = auth.uid()
  )
);

-- 8. Criar políticas RLS para patient_questionnaire_responses
CREATE POLICY "Users can view responses for their assignments" 
ON patient_questionnaire_responses FOR SELECT 
USING (
  assignment_id IN (
    SELECT id FROM patient_questionnaire_assignments 
    WHERE assigned_by = auth.uid() OR 
          patient_id IN (
            SELECT id FROM patients WHERE user_id = auth.uid()
          )
  )
);

CREATE POLICY "Users can insert responses for their assignments" 
ON patient_questionnaire_responses FOR INSERT 
WITH CHECK (
  assignment_id IN (
    SELECT id FROM patient_questionnaire_assignments 
    WHERE assigned_by = auth.uid() OR 
          patient_id IN (
            SELECT id FROM patients WHERE user_id = auth.uid()
          )
  )
);

CREATE POLICY "Users can update responses for their assignments" 
ON patient_questionnaire_responses FOR UPDATE 
USING (
  assignment_id IN (
    SELECT id FROM patient_questionnaire_assignments 
    WHERE assigned_by = auth.uid() OR 
          patient_id IN (
            SELECT id FROM patients WHERE user_id = auth.uid()
          )
  )
);

CREATE POLICY "Users can delete responses for their assignments" 
ON patient_questionnaire_responses FOR DELETE 
USING (
  assignment_id IN (
    SELECT id FROM patient_questionnaire_assignments 
    WHERE assigned_by = auth.uid() OR 
          patient_id IN (
            SELECT id FROM patients WHERE user_id = auth.uid()
          )
  )
);

-- =====================================================
-- SCRIPT CONCLUÍDO
-- =====================================================

-- Para verificar se as tabelas foram criadas corretamente:
-- SELECT table_name FROM information_schema.tables 
-- WHERE table_schema = 'public' 
-- AND table_name LIKE '%questionnaire%';
