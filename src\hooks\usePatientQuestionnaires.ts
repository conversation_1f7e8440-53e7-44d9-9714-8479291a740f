import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { toast } from '@/hooks/use-toast';
import type { 
  AssignmentWithQuestionnaire, 
  Response, 
  QuestionnaireAssignmentFormData,
  QuestionnaireResponseFormData 
} from '@/schemas/questionnaire';

// Query keys
export const PATIENT_QUESTIONNAIRES_KEYS = {
  all: ['patient-questionnaires'] as const,
  assignments: (patientId: string) => [...PATIENT_QUESTIONNAIRES_KEYS.all, 'assignments', patientId] as const,
  responses: (assignmentId: string) => [...PATIENT_QUESTIONNAIRES_KEYS.all, 'responses', assignmentId] as const,
};

// Hook to fetch patient questionnaire assignments
export function usePatientQuestionnaireAssignments(patientId: string) {
  const { user } = useAuth();

  return useQuery({
    queryKey: PATIENT_QUESTIONNAIRES_KEYS.assignments(patientId),
    queryFn: async (): Promise<AssignmentWithQuestionnaire[]> => {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('patient_questionnaire_assignments')
        .select(`
          *,
          questionnaire:questionnaires(
            id,
            title,
            description,
            user_id,
            created_at,
            updated_at,
            questions:questionnaire_questions(
              id,
              question_text,
              question_order,
              questionnaire_id,
              created_at
            )
          )
        `)
        .eq('patient_id', patientId)
        .order('assigned_at', { ascending: false });

      if (error) {
        console.error('Error fetching patient questionnaire assignments:', error);
        throw error;
      }

      return (data || []) as AssignmentWithQuestionnaire[];
    },
    enabled: !!user && !!patientId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to fetch questionnaire responses for an assignment
export function useQuestionnaireResponses(assignmentId: string) {
  const { user } = useAuth();

  return useQuery({
    queryKey: PATIENT_QUESTIONNAIRES_KEYS.responses(assignmentId),
    queryFn: async (): Promise<Response[]> => {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('patient_questionnaire_responses')
        .select('*')
        .eq('assignment_id', assignmentId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching questionnaire responses:', error);
        throw error;
      }

      return (data || []) as Response[];
    },
    enabled: !!user && !!assignmentId,
    staleTime: 10 * 60 * 1000, // 10 minutes (responses don't change often)
  });
}

// Hook to assign a questionnaire to a patient
export function useAssignQuestionnaire() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (data: QuestionnaireAssignmentFormData) => {
      if (!user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('patient_questionnaire_assignments')
        .insert({
          patient_id: data.patientId,
          questionnaire_id: data.questionnaireId,
          assigned_by: user.id,
          status: 'pending',
        });

      if (error) throw error;
    },
    onSuccess: (_, variables) => {
      // Invalidate and refetch assignments for this patient
      queryClient.invalidateQueries({
        queryKey: PATIENT_QUESTIONNAIRES_KEYS.assignments(variables.patientId),
      });

      toast({
        title: 'Sucesso',
        description: 'Questionário atribuído ao paciente com sucesso',
      });
    },
    onError: (error) => {
      console.error('Error assigning questionnaire:', error);
      toast({
        title: 'Erro',
        description: 'Erro ao atribuir questionário ao paciente',
        variant: 'destructive',
      });
    },
  });
}

// Hook to submit questionnaire responses
export function useSubmitQuestionnaireResponses() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (data: QuestionnaireResponseFormData) => {
      if (!user) throw new Error('User not authenticated');

      // Start a transaction-like operation
      // First, insert all responses
      const { error: responsesError } = await supabase
        .from('patient_questionnaire_responses')
        .insert(
          data.responses.map(response => ({
            assignment_id: data.assignmentId,
            question_id: response.questionId,
            response_text: response.responseText,
          }))
        );

      if (responsesError) throw responsesError;

      // Then, update the assignment status to completed
      const { error: assignmentError } = await supabase
        .from('patient_questionnaire_assignments')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
        })
        .eq('id', data.assignmentId);

      if (assignmentError) throw assignmentError;

      return data.assignmentId;
    },
    onSuccess: (assignmentId, variables) => {
      // Get patient ID from the assignment to invalidate the correct queries
      const assignment = queryClient.getQueryData<AssignmentWithQuestionnaire[]>(
        PATIENT_QUESTIONNAIRES_KEYS.assignments('')
      )?.find(a => a.id === assignmentId);

      if (assignment) {
        // Invalidate assignments for this patient
        queryClient.invalidateQueries({
          queryKey: PATIENT_QUESTIONNAIRES_KEYS.assignments(assignment.patient_id),
        });
      }

      // Set the responses in cache
      queryClient.setQueryData(
        PATIENT_QUESTIONNAIRES_KEYS.responses(variables.assignmentId),
        variables.responses.map((response, index) => ({
          id: `temp-${index}`, // Temporary ID
          assignment_id: variables.assignmentId,
          question_id: response.questionId,
          response_text: response.responseText,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }))
      );

      toast({
        title: 'Sucesso',
        description: 'Respostas enviadas com sucesso',
      });
    },
    onError: (error) => {
      console.error('Error submitting questionnaire responses:', error);
      toast({
        title: 'Erro',
        description: 'Erro ao enviar respostas do questionário',
        variant: 'destructive',
      });
    },
  });
}
