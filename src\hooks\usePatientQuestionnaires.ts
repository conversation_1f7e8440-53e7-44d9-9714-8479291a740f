import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/context/AuthContext";
import { toast } from "@/hooks/use-toast";
import {
  getPatientQuestionnaireAssignments,
  getQuestionnaireResponses,
  assignQuestionnaireToPatient,
  submitQuestionnaireResponses,
} from "@/http/patient-questionnaires";
import type {
  AssignmentWithQuestionnaire,
  Response,
  QuestionnaireAssignmentFormData,
  QuestionnaireResponseFormData,
} from "@/schemas/questionnaire";

// Query keys
export const PATIENT_QUESTIONNAIRES_KEYS = {
  all: ["patient-questionnaires"] as const,
  assignments: (patientId: string) =>
    [...PATIENT_QUESTIONNAIRES_KEYS.all, "assignments", patientId] as const,
  responses: (assignmentId: string) =>
    [...PATIENT_QUESTIONNAIRES_KEYS.all, "responses", assignmentId] as const,
};

// Hook to fetch patient questionnaire assignments
export function usePatientQuestionnaireAssignments(patientId: string) {
  const { user } = useAuth();

  return useQuery({
    queryKey: PATIENT_QUESTIONNAIRES_KEYS.assignments(patientId),
    queryFn: async (): Promise<AssignmentWithQuestionnaire[]> => {
      if (!user) throw new Error("User not authenticated");
      return getPatientQuestionnaireAssignments(patientId);
    },
    enabled: !!user && !!patientId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to fetch questionnaire responses for an assignment
export function useQuestionnaireResponses(assignmentId: string) {
  const { user } = useAuth();

  return useQuery({
    queryKey: PATIENT_QUESTIONNAIRES_KEYS.responses(assignmentId),
    queryFn: async (): Promise<Response[]> => {
      if (!user) throw new Error("User not authenticated");
      return getQuestionnaireResponses(assignmentId);
    },
    enabled: !!user && !!assignmentId,
    staleTime: 10 * 60 * 1000, // 10 minutes (responses don't change often)
  });
}

// Hook to assign a questionnaire to a patient
export function useAssignQuestionnaire() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (data: QuestionnaireAssignmentFormData) => {
      if (!user) throw new Error("User not authenticated");
      return assignQuestionnaireToPatient(data, user.id);
    },
    onSuccess: (_, variables) => {
      // Invalidate and refetch assignments for this patient
      queryClient.invalidateQueries({
        queryKey: PATIENT_QUESTIONNAIRES_KEYS.assignments(variables.patientId),
      });

      toast({
        title: "Sucesso",
        description: "Questionário atribuído ao paciente com sucesso",
      });
    },
    onError: (error) => {
      console.error("Error assigning questionnaire:", error);
      toast({
        title: "Erro",
        description: "Erro ao atribuir questionário ao paciente",
        variant: "destructive",
      });
    },
  });
}

// Hook to submit questionnaire responses
export function useSubmitQuestionnaireResponses() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (data: QuestionnaireResponseFormData) => {
      if (!user) throw new Error("User not authenticated");
      await submitQuestionnaireResponses(data);
      return data.assignmentId;
    },
    onSuccess: (_, variables) => {
      // Invalidate all assignment queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: PATIENT_QUESTIONNAIRES_KEYS.all,
      });

      // Set the responses in cache
      queryClient.setQueryData(
        PATIENT_QUESTIONNAIRES_KEYS.responses(variables.assignmentId),
        variables.responses.map((response, index) => ({
          id: `temp-${index}`, // Temporary ID
          assignment_id: variables.assignmentId,
          question_id: response.questionId,
          response_text: response.responseText,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }))
      );

      toast({
        title: "Sucesso",
        description: "Respostas enviadas com sucesso",
      });
    },
    onError: (error) => {
      console.error("Error submitting questionnaire responses:", error);
      toast({
        title: "Erro",
        description: "Erro ao enviar respostas do questionário",
        variant: "destructive",
      });
    },
  });
}
