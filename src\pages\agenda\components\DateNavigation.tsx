import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { format, startOfWeek, addDays } from "date-fns";
import { ptBR } from "date-fns/locale";
import type { ViewMode } from "../types";

interface DateNavigationProps {
  currentDate: Date;
  viewMode: ViewMode;
  setViewMode: (mode: ViewMode) => void;
  onNavigatePrevious: () => void;
  onNavigateNext: () => void;
  onGoToToday: () => void;
}

export function DateNavigation({
  currentDate,
  viewMode,
  setViewMode,
  onNavigatePrevious,
  onNavigateNext,
  onGoToToday,
}: DateNavigationProps) {
  const getDateTitle = () => {
    if (viewMode === "week") {
      const weekStart = startOfWeek(currentDate, { weekStartsOn: 1 });
      const weekEnd = addDays(weekStart, 6);
      return `${format(weekStart, "dd MMM", { locale: ptBR })} - ${format(
        weekEnd,
        "dd MMM yyyy",
        { locale: ptBR }
      )}`;
    }
    return format(currentDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR });
  };

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-1">
          <Button
            variant="outline"
            size="sm"
            onClick={onNavigatePrevious}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onGoToToday}
          >
            Hoje
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onNavigateNext}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>

        <h2 className="text-xl font-semibold">
          {getDateTitle()}
        </h2>
      </div>

      <div className="flex items-center space-x-2">
        <Button
          variant={viewMode === "day" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("day")}
        >
          Dia
        </Button>
        <Button
          variant={viewMode === "week" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("week")}
        >
          Semana
        </Button>
      </div>
    </div>
  );
}
