# Guia de Questionários para Pacientes

## Visão Geral

Foi criada uma nova funcionalidade que permite aos pacientes visualizar e responder questionários atribuídos pelos psicólogos. Esta funcionalidade está disponível através da rota `/patient/questionnaires`.

## Funcionalidades Implementadas

### 1. **Página Principal de Questionários do Paciente**
- **Rota**: `/patient/questionnaires`
- **Componente**: `PatientQuestionnaires.tsx`
- **Funcionalidades**:
  - Lista todos os questionários atribuídos ao paciente logado
  - Separação entre questionários pendentes e concluídos
  - Contadores de status (pendentes/concluídos)
  - Interface responsiva e intuitiva

### 2. **Responder Questionários**
- **Componente**: `PatientQuestionnaireForm.tsx`
- **Funcionalidades**:
  - Formulário para responder questionários pendentes
  - Validação com React Hook Form e Zod
  - Barra de progresso mostrando perguntas respondidas
  - Possibilidade de enviar questionário parcialmente preenchido
  - Feedback visual durante o envio

### 3. **Visualizar Respostas**
- **Componente**: `PatientQuestionnaireResponses.tsx`
- **Funcionalidades**:
  - Visualização de questionários já respondidos
  - Exibição das respostas em formato somente leitura
  - Informações sobre datas de atribuição e conclusão
  - Indicação de perguntas não respondidas

## Arquitetura Técnica

### **Hooks Personalizados**
- `useMyQuestionnaireAssignments()`: Busca questionários do paciente logado
- `useQuestionnaireResponsesView()`: Busca respostas de um questionário específico
- `useSubmitQuestionnaireResponsesPatient()`: Envia respostas do questionário
- `useQuestionnaireAssignmentView()`: Busca um questionário específico

### **Funções HTTP**
- `getMyQuestionnaireAssignments()`: Busca questionários baseado no user_id
- Reutiliza funções existentes para respostas e submissão

### **Validação e Tipos**
- Utiliza schemas Zod existentes para validação
- TypeScript para type safety completo
- Integração com React Hook Form

## Como Usar

### **Para Psicólogos**
1. Continue usando a funcionalidade existente em `/questionarios`
2. Atribua questionários aos pacientes normalmente
3. Os pacientes poderão acessar seus questionários na nova interface

### **Para Pacientes**
1. Acesse a aplicação com suas credenciais
2. Navegue para "Meus Questionários" no menu lateral
3. Visualize questionários pendentes e concluídos
4. Clique em "Responder Questionário" para questionários pendentes
5. Preencha as respostas e envie
6. Visualize respostas anteriores clicando em "Ver Minhas Respostas"

## Navegação

### **Menu Lateral**
- Adicionada seção "Área do Paciente" com link "Meus Questionários"
- Link direto para `/patient/questionnaires`

### **Estados da Interface**
- **Lista**: Visualização principal com todos os questionários
- **Responder**: Formulário para responder questionário pendente
- **Visualizar**: Exibição de respostas já enviadas

## Segurança e Permissões

### **Autenticação**
- Apenas usuários autenticados podem acessar
- Pacientes só veem questionários atribuídos a eles
- Utiliza Row Level Security (RLS) do Supabase

### **Autorização**
- Função `getMyQuestionnaireAssignments()` verifica se o usuário tem registro na tabela `patients`
- Políticas RLS garantem isolamento de dados entre pacientes

## Tecnologias Utilizadas

- **React Hook Form**: Gerenciamento de formulários
- **Zod**: Validação de schemas
- **React Query**: Cache e gerenciamento de estado
- **Supabase**: Backend e autenticação
- **Tailwind CSS**: Estilização
- **Shadcn/ui**: Componentes de interface

## Próximos Passos

### **Melhorias Futuras**
1. **Notificações**: Alertar pacientes sobre novos questionários
2. **Histórico**: Timeline de questionários respondidos
3. **Filtros**: Busca e filtros por data/status
4. **Exportação**: Download de respostas em PDF
5. **Lembretes**: Sistema de lembretes para questionários pendentes

### **Configuração do Banco de Dados**
- Execute o script `database-setup.sql` no Supabase SQL Editor
- Verifique se as tabelas `patient_questionnaire_assignments` e `patient_questionnaire_responses` foram criadas
- Confirme que as políticas RLS estão ativas

## Troubleshooting

### **Problemas Comuns**
1. **Questionários não aparecem**: Verifique se o usuário tem registro na tabela `patients`
2. **Erro ao enviar respostas**: Confirme se as tabelas do banco foram criadas
3. **Permissões negadas**: Verifique as políticas RLS no Supabase

### **Logs e Debug**
- Erros são logados no console do navegador
- Mensagens de toast informam sobre sucessos/erros
- React Query DevTools podem ajudar no debug de queries

## Conclusão

A funcionalidade de questionários para pacientes está completa e pronta para uso. Ela oferece uma interface intuitiva e segura para que pacientes possam responder questionários atribuídos pelos psicólogos, mantendo a separação clara entre as funcionalidades de psicólogos e pacientes no sistema.
