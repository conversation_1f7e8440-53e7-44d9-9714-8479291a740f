import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";
import {
  getMyQuestionnaireAssignments,
  getQuestionnaireResponses,
  submitQuestionnaireResponses,
} from "@/http/patient-questionnaires";
import type {
  AssignmentWithQuestionnaire,
  Response,
  QuestionnaireResponseFormData,
} from "@/schemas/questionnaire";

// Query keys for patient questionnaire view
export const PATIENT_QUESTIONNAIRE_KEYS = {
  all: ["patient-questionnaires"] as const,
  myAssignments: (userId: string) => [...PATIENT_QUESTIONNAIRE_KEYS.all, "my-assignments", userId] as const,
  responses: (assignmentId: string) => [...PATIENT_QUESTIONNAIRE_KEYS.all, "responses", assignmentId] as const,
};

/**
 * Hook to fetch questionnaire assignments for the current patient
 */
export function useMyQuestionnaireAssignments() {
  const { user } = useAuth();

  return useQuery({
    queryKey: PATIENT_QUESTIONNAIRE_KEYS.myAssignments(user?.id || ""),
    queryFn: async (): Promise<AssignmentWithQuestionnaire[]> => {
      if (!user) throw new Error("User not authenticated");
      return getMyQuestionnaireAssignments(user.id);
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch responses for a specific assignment (for viewing completed questionnaires)
 */
export function useQuestionnaireResponsesView(assignmentId: string) {
  const { user } = useAuth();

  return useQuery({
    queryKey: PATIENT_QUESTIONNAIRE_KEYS.responses(assignmentId),
    queryFn: async (): Promise<Response[]> => {
      if (!user) throw new Error("User not authenticated");
      return getQuestionnaireResponses(assignmentId);
    },
    enabled: !!user && !!assignmentId,
    staleTime: 10 * 60 * 1000, // 10 minutes (responses don't change often)
  });
}

/**
 * Hook to submit questionnaire responses (patient completing a questionnaire)
 */
export function useSubmitQuestionnaireResponsesPatient() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (data: QuestionnaireResponseFormData) => {
      await submitQuestionnaireResponses(data);
      return data.assignmentId;
    },
    onSuccess: (assignmentId, variables) => {
      // Invalidate all patient questionnaire queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: PATIENT_QUESTIONNAIRE_KEYS.all,
      });

      // Set the responses in cache for immediate viewing
      queryClient.setQueryData(
        PATIENT_QUESTIONNAIRE_KEYS.responses(variables.assignmentId),
        variables.responses.map((response, index) => ({
          id: `temp-${index}`, // Temporary ID
          assignment_id: variables.assignmentId,
          question_id: response.questionId,
          response_text: response.responseText,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }))
      );

      toast({
        title: "Questionário Enviado",
        description: "Suas respostas foram enviadas com sucesso!",
      });
    },
    onError: (error) => {
      console.error("Error submitting questionnaire responses:", error);
      toast({
        title: "Erro",
        description: "Erro ao enviar respostas do questionário. Tente novamente.",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook to get assignment by ID (for patient view)
 */
export function useQuestionnaireAssignmentView(assignmentId: string) {
  const { user } = useAuth();
  const { data: assignments } = useMyQuestionnaireAssignments();

  return {
    assignment: assignments?.find(a => a.id === assignmentId),
    isLoading: !assignments && !!user,
  };
}
