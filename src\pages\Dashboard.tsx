import { usePatients } from "@/context/PatientsContext";
import { useAppointments } from "@/context/AppointmentsContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, Calendar, FileText, TrendingUp } from "lucide-react";

const Dashboard = () => {
  const { patients } = usePatients();
  const { appointments } = useAppointments();

  const totalPatients = patients.length;
  const totalConsultations = patients.reduce(
    (total, patient) => total + patient.consultationSummaries.length, 
    0
  );
  
  // Consultas desta semana (mock)
  const consultationsThisWeek = Math.floor(totalConsultations * 0.3);
  
  // Pacientes ativos (com consulta nos últimos 30 dias)
  const activePatients = patients.filter(patient => {
    const lastConsultation = patient.consultationSummaries[patient.consultationSummaries.length - 1];
    if (!lastConsultation) return false;
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    return lastConsultation.date >= thirtyDaysAgo;
  }).length;

  // Calcular estatísticas dos agendamentos
  const today = new Date().toISOString().split('T')[0];
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const tomorrowStr = tomorrow.toISOString().split('T')[0];

  const appointmentsToday = appointments.filter(apt => 
    apt.appointment_date === today && apt.status === 'scheduled'
  ).length;

  const appointmentsTomorrow = appointments.filter(apt => 
    apt.appointment_date === tomorrowStr && apt.status === 'scheduled'
  ).length;

  // Próximo horário disponível (simplificado)
  const todayAppointments = appointments
    .filter(apt => apt.appointment_date === today && apt.status === 'scheduled')
    .sort((a, b) => a.start_time.localeCompare(b.start_time));

  let nextAvailable = "Sem agenda";
  if (todayAppointments.length > 0) {
    const lastAppointment = todayAppointments[todayAppointments.length - 1];
    nextAvailable = lastAppointment.end_time;
  } else {
    nextAvailable = "08:00";
  }

  // Taxa de ocupação (calculada baseada nos agendamentos da semana)
  const weekStart = new Date();
  weekStart.setDate(weekStart.getDate() - weekStart.getDay());
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekStart.getDate() + 6);

  const weekAppointments = appointments.filter(apt => {
    const aptDate = new Date(apt.appointment_date);
    return aptDate >= weekStart && aptDate <= weekEnd && apt.status === 'scheduled';
  });

  // Assumindo 8 horas de trabalho por dia, 5 dias por semana = 40 horas
  const totalWorkingHours = 40;
  const bookedHours = weekAppointments.reduce((total, apt) => {
    const start = apt.start_time.split(':');
    const end = apt.end_time.split(':');
    const startMinutes = parseInt(start[0]) * 60 + parseInt(start[1]);
    const endMinutes = parseInt(end[0]) * 60 + parseInt(end[1]);
    return total + (endMinutes - startMinutes) / 60;
  }, 0);

  const occupancyRate = Math.round((bookedHours / totalWorkingHours) * 100);

  const recentPatients = patients
    .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
    .slice(0, 5);

  const stats = [
    {
      title: "Total de Pacientes",
      value: totalPatients,
      icon: Users,
      color: "text-primary",
      bgColor: "bg-primary/10"
    },
    {
      title: "Consultas Realizadas",
      value: totalConsultations,
      icon: Calendar,
      color: "text-secondary",
      bgColor: "bg-secondary/10"
    },
    {
      title: "Consultas esta Semana",
      value: consultationsThisWeek,
      icon: TrendingUp,
      color: "text-info",
      bgColor: "bg-info/10"
    },
    {
      title: "Pacientes Ativos",
      value: activePatients,
      icon: FileText,
      color: "text-success",
      bgColor: "bg-success/10"
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground mb-2">Dashboard</h1>
        <p className="text-muted-foreground">
          Bem-vindo ao seu sistema de gestão psicológica
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`w-6 h-6 ${stat.color}`} />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-foreground">
                    {stat.value}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Patients */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="w-5 h-5" />
              <span>Pacientes Recentes</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentPatients.map((patient) => (
                <div 
                  key={patient.id}
                  className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                >
                  <div>
                    <p className="font-medium text-foreground">{patient.name}</p>
                    <p className="text-sm text-muted-foreground">
                      Última atualização: {patient.updatedAt.toLocaleDateString('pt-BR')}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-primary">
                      {patient.consultationSummaries.length} consultas
                    </p>
                  </div>
                </div>
              ))}
              {recentPatients.length === 0 && (
                <p className="text-center text-muted-foreground py-8">
                  Nenhum paciente cadastrado ainda
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="w-5 h-5" />
              <span>Resumo de Atividades</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                <span className="text-foreground">Consultas Hoje</span>
                <span className="font-bold text-primary">{appointmentsToday}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                <span className="text-foreground">Consultas Amanhã</span>
                <span className="font-bold text-secondary">{appointmentsTomorrow}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                <span className="text-foreground">Próx. Disponível</span>
                <span className="font-bold text-info">{nextAvailable}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                <span className="text-foreground">Taxa de Ocupação</span>
                <span className="font-bold text-success">{occupancyRate}%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;