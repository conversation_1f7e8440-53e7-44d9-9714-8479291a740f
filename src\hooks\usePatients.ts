import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import {
  getPatients,
  createPatient,
  updatePatient,
  deletePatient,
  createConsultation,
  updateConsultation,
  deleteConsultation,
  type CreatePatientData,
  type UpdatePatientData,
  type CreateConsultationData,
  type UpdateConsultationData,
} from "@/http/patients";

const PATIENTS_QUERY_KEY = "patients";

/**
 * Hook for fetching patients with React Query
 */
export function usePatientsQuery() {
  const { user } = useAuth();

  return useQuery({
    queryKey: [PATIENTS_QUERY_KEY, user?.id],
    queryFn: () => {
      if (!user?.id) {
        throw new Error("User not authenticated");
      }
      return getPatients(user.id);
    },
    enabled: !!user?.id,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook for creating a new patient
 */
export function useCreatePatient() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (patientData: CreatePatientData) => {
      if (!user?.id) {
        throw new Error("User not authenticated");
      }
      return createPatient(user.id, patientData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PATIENTS_QUERY_KEY] });
      toast({
        title: "Sucesso",
        description: "Paciente adicionado com sucesso",
      });
    },
    onError: (error) => {
      console.error("Error adding patient:", error);
      toast({
        title: "Erro",
        description: "Erro ao adicionar paciente",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook for updating a patient
 */
export function useUpdatePatient() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: UpdatePatientData }) =>
      updatePatient(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PATIENTS_QUERY_KEY] });
      toast({
        title: "Sucesso",
        description: "Paciente atualizado com sucesso",
      });
    },
    onError: (error) => {
      console.error("Error updating patient:", error);
      toast({
        title: "Erro",
        description: "Erro ao atualizar paciente",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook for deleting a patient
 */
export function useDeletePatient() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (patientId: string) => deletePatient(patientId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PATIENTS_QUERY_KEY] });
      toast({
        title: "Sucesso",
        description: "Paciente removido com sucesso",
      });
    },
    onError: (error) => {
      console.error("Error deleting patient:", error);
      toast({
        title: "Erro",
        description: "Erro ao remover paciente",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook for creating a consultation
 */
export function useCreateConsultation() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      patientId,
      consultationData,
    }: {
      patientId: string;
      consultationData: CreateConsultationData;
    }) => createConsultation(patientId, consultationData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PATIENTS_QUERY_KEY] });
      toast({
        title: "Sucesso",
        description: "Consulta adicionada com sucesso",
      });
    },
    onError: (error) => {
      console.error("Error adding consultation:", error);
      toast({
        title: "Erro",
        description: "Erro ao adicionar consulta",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook for updating a consultation
 */
export function useUpdateConsultation() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      consultationId,
      updates,
    }: {
      consultationId: string;
      updates: UpdateConsultationData;
    }) => updateConsultation(consultationId, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PATIENTS_QUERY_KEY] });
      toast({
        title: "Sucesso",
        description: "Consulta atualizada com sucesso",
      });
    },
    onError: (error) => {
      console.error("Error updating consultation:", error);
      toast({
        title: "Erro",
        description: "Erro ao atualizar consulta",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook for deleting a consultation
 */
export function useDeleteConsultation() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (consultationId: string) => deleteConsultation(consultationId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PATIENTS_QUERY_KEY] });
      toast({
        title: "Sucesso",
        description: "Consulta excluída com sucesso",
      });
    },
    onError: (error) => {
      console.error("Error deleting consultation:", error);
      toast({
        title: "Erro",
        description: "Erro ao excluir consulta",
        variant: "destructive",
      });
    },
  });
}
