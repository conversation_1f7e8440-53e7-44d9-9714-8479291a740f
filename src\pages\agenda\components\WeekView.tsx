import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { format, addDays, startOfWeek } from "date-fns";
import { ptBR } from "date-fns/locale";
import type { Appointment } from "@/context/AppointmentsContext";
import { statusColors, statusLabels } from "../types";

interface WeekViewProps {
  currentDate: Date;
  appointments: Appointment[];
  onEdit: (appointment: Appointment) => void;
}

export function WeekView({
  currentDate,
  appointments,
  onEdit,
}: WeekViewProps) {
  const getWeekDays = () => {
    const start = startOfWeek(currentDate, { weekStartsOn: 1 });
    return Array.from({ length: 7 }, (_, i) => addDays(start, i));
  };

  const getAppointmentsForDate = (date: Date) => {
    const dateStr = format(date, "yyyy-MM-dd");
    return appointments
      .filter((apt) => apt.appointment_date === dateStr)
      .sort((a, b) => a.start_time.localeCompare(b.start_time));
  };

  const weekDays = getWeekDays();

  return (
    <div className="flex flex-col gap-4">
      {weekDays.map((day, index) => {
        const dayAppointments = getAppointmentsForDate(day);
        const isToday =
          format(day, "yyyy-MM-dd") === format(new Date(), "yyyy-MM-dd");

        return (
          <Card
            key={index}
            className={`min-h-[100px] ${
              isToday ? "ring-2 ring-primary" : ""
            }`}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-center">
                <div className="text-xs text-muted-foreground">
                  {format(day, "EEE", { locale: ptBR })}
                </div>
                <div
                  className={`text-lg ${
                    isToday ? "text-primary font-bold" : ""
                  }`}
                >
                  {format(day, "d")}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {dayAppointments.map((appointment) => (
                <div
                  key={appointment.id}
                  className="py-2 rounded-md bg-muted/50 border text-xs cursor-pointer hover:bg-muted text-center"
                  onClick={() => onEdit(appointment)}
                >
                  <div className="flex flex-col items-center justify-between mb-1">
                    <Badge
                      variant="outline"
                      className={`text-xs ${
                        statusColors[appointment.status]
                      }`}
                    >
                      {statusLabels[appointment.status]}
                    </Badge>
                    <span className="font-medium">
                      {appointment.start_time}
                    </span>
                  </div>
                  <div className="text-foreground font-medium">
                    {appointment.title}
                  </div>
                  {appointment.patient && (
                    <div className="text-muted-foreground">
                      {appointment.patient.name}
                    </div>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
