import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import {
  usePatientQuestionnaireAssignments,
  useQuestionnaireResponses,
  useSubmitQuestionnaireResponses,
} from "@/hooks/usePatientQuestionnaires";
import {
  questionnaireResponseFormSchema,
  type AssignmentWithQuestionnaire,
  type QuestionnaireResponseFormData,
} from "@/schemas/questionnaire";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { <PERSON><PERSON><PERSON><PERSON>, Clock, Eye, FileQuestion, Loader2 } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { PatientQuestionnaireAssignment } from "./PatientQuestionnaireAssignment";

interface PatientQuestionnairesListProps {
  patientId: string;
}

export function PatientQuestionnairesList({
  patientId,
}: PatientQuestionnairesListProps) {
  const [selectedAssignment, setSelectedAssignment] =
    useState<AssignmentWithQuestionnaire | null>(null);
  const [isViewingResponses, setIsViewingResponses] = useState(false);

  // React Query hooks
  const {
    data: assignments = [],
    isLoading: assignmentsLoading,
    error: assignmentsError,
  } = usePatientQuestionnaireAssignments(patientId);

  const { data: responses = [], isLoading: responsesLoading } =
    useQuestionnaireResponses(selectedAssignment?.id || "");

  const submitResponsesMutation = useSubmitQuestionnaireResponses();

  // Form for questionnaire responses
  const form = useForm<QuestionnaireResponseFormData>({
    resolver: zodResolver(questionnaireResponseFormSchema),
    defaultValues: {
      assignmentId: "",
      responses: [],
    },
  });

  // Event handlers
  const handleViewResponses = (assignment: AssignmentWithQuestionnaire) => {
    setSelectedAssignment(assignment);
    setIsViewingResponses(true);
  };

  const handleAnswerQuestionnaire = (
    assignment: AssignmentWithQuestionnaire
  ) => {
    setSelectedAssignment(assignment);
    setIsViewingResponses(false);

    // Initialize form with assignment data
    const initialResponses =
      assignment.questionnaire.questions?.map((q) => ({
        questionId: q.id,
        responseText: "",
      })) || [];

    form.reset({
      assignmentId: assignment.id,
      responses: initialResponses,
    });
  };

  const onSubmitResponses = async (data: QuestionnaireResponseFormData) => {
    try {
      await submitResponsesMutation.mutateAsync(data);
      setSelectedAssignment(null);
      form.reset();
    } catch (error) {
      // Error handling is done in the mutation hook
      console.error("Failed to submit responses:", error);
    }
  };

  const handleCloseDialog = () => {
    setSelectedAssignment(null);
    setIsViewingResponses(false);
    form.reset();
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <FileQuestion className="w-5 h-5" />
          Questionários
        </h3>
        <PatientQuestionnaireAssignment patientId={patientId} />
      </div>

      {assignmentsLoading ? (
        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center">
              <Loader2 className="w-6 h-6 animate-spin mr-2" />
              Carregando questionários...
            </div>
          </CardContent>
        </Card>
      ) : assignmentsError ? (
        <Card>
          <CardContent className="p-6 text-center text-destructive">
            Erro ao carregar questionários. Tente novamente.
          </CardContent>
        </Card>
      ) : assignments.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center text-muted-foreground">
            Nenhum questionário atribuído a este paciente.
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {assignments.map((assignment) => (
            <Card key={assignment.id}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">
                    {assignment.questionnaire.title}
                  </CardTitle>
                  <Badge
                    variant={
                      assignment.status === "completed"
                        ? "default"
                        : "secondary"
                    }
                  >
                    {assignment.status === "completed" ? (
                      <>
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Respondido
                      </>
                    ) : (
                      <>
                        <Clock className="w-3 h-3 mr-1" />
                        Pendente
                      </>
                    )}
                  </Badge>
                </div>
                {assignment.questionnaire.description && (
                  <p className="text-sm text-muted-foreground">
                    {assignment.questionnaire.description}
                  </p>
                )}
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    Atribuído em{" "}
                    {format(new Date(assignment.assigned_at), "dd/MM/yyyy", {
                      locale: ptBR,
                    })}
                    {assignment.completed_at && (
                      <>
                        {" • "}
                        Respondido em{" "}
                        {format(
                          new Date(assignment.completed_at),
                          "dd/MM/yyyy",
                          { locale: ptBR }
                        )}
                      </>
                    )}
                  </span>
                  <div className="flex gap-2">
                    {assignment.status === "completed" ? (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewResponses(assignment)}
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        Ver Respostas
                      </Button>
                    ) : (
                      <Button
                        size="sm"
                        onClick={() => handleAnswerQuestionnaire(assignment)}
                      >
                        Responder
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Dialog para responder/visualizar questionário */}
      <Dialog open={!!selectedAssignment} onOpenChange={handleCloseDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {isViewingResponses
                ? "Respostas do Questionário"
                : "Responder Questionário"}
              {selectedAssignment &&
                ` - ${selectedAssignment.questionnaire.title}`}
            </DialogTitle>
          </DialogHeader>

          {selectedAssignment && (
            <>
              {isViewingResponses ? (
                // View responses mode
                <div className="space-y-4">
                  {responsesLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="w-6 h-6 animate-spin" />
                      <span className="ml-2">Carregando respostas...</span>
                    </div>
                  ) : (
                    selectedAssignment.questionnaire.questions
                      ?.sort((a, b) => a.question_order - b.question_order)
                      .map((question, index) => {
                        const response = responses.find(
                          (r) => r.question_id === question.id
                        );
                        return (
                          <div key={question.id} className="space-y-2">
                            <div className="text-sm font-medium">
                              {index + 1}. {question.question_text}
                            </div>
                            <div className="p-3 bg-muted rounded-md min-h-[80px]">
                              {response?.response_text || "Sem resposta"}
                            </div>
                          </div>
                        );
                      })
                  )}
                </div>
              ) : (
                // Answer questionnaire mode with React Hook Form
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmitResponses)}
                    className="space-y-4"
                  >
                    {selectedAssignment.questionnaire.questions
                      ?.sort((a, b) => a.question_order - b.question_order)
                      .map((question, index) => (
                        <FormField
                          key={question.id}
                          control={form.control}
                          name={`responses.${index}.responseText`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-medium">
                                {index + 1}. {question.question_text}
                              </FormLabel>
                              <FormControl>
                                <Textarea
                                  {...field}
                                  placeholder="Digite sua resposta..."
                                  className="min-h-[80px]"
                                  disabled={submitResponsesMutation.isPending}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      ))}

                    <div className="flex justify-end gap-2 pt-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleCloseDialog}
                        disabled={submitResponsesMutation.isPending}
                      >
                        Cancelar
                      </Button>
                      <Button
                        type="submit"
                        disabled={submitResponsesMutation.isPending}
                      >
                        {submitResponsesMutation.isPending ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Enviando...
                          </>
                        ) : (
                          "Enviar Respostas"
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              )}
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
