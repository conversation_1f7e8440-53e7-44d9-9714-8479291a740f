import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { PatientQuestionnaireAssignment } from './PatientQuestionnaireAssignment';
import { supabase } from '@/integrations/supabase/client';
import { FileQuestion, Clock, CheckCircle, Eye } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface Question {
  id: string;
  question_text: string;
  question_order: number;
}

interface Questionnaire {
  id: string;
  title: string;
  description?: string;
  questions?: Question[];
}

interface Assignment {
  id: string;
  questionnaire: Questionnaire;
  status: 'pending' | 'completed';
  assigned_at: string;
  completed_at?: string;
}

interface Response {
  question_id: string;
  response_text: string;
}

interface PatientQuestionnairesListProps {
  patientId: string;
}

export function PatientQuestionnairesList({ patientId }: PatientQuestionnairesListProps) {
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAssignment, setSelectedAssignment] = useState<Assignment | null>(null);
  const [responses, setResponses] = useState<Response[]>([]);
  const [isViewingResponses, setIsViewingResponses] = useState(false);

  const fetchAssignments = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('patient_questionnaire_assignments' as any)
        .select(`
          *,
          questionnaire:questionnaires(
            id,
            title,
            description,
            questions:questionnaire_questions(
              id,
              question_text,
              question_order
            )
          )
        `)
        .eq('patient_id', patientId)
        .order('assigned_at', { ascending: false });

      if (error) {
        console.error('Erro ao carregar questionários:', error);
        return;
      }

      const formattedAssignments = data?.map((item: any) => ({
        id: item.id,
        questionnaire: item.questionnaire,
        status: item.status,
        assigned_at: item.assigned_at,
        completed_at: item.completed_at
      })) || [];

      setAssignments(formattedAssignments);
    } catch (error) {
      console.error('Erro ao carregar questionários:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAssignments();
  }, [patientId]);

  const handleViewResponses = async (assignment: Assignment) => {
    setSelectedAssignment(assignment);
    setIsViewingResponses(true);
    
    try {
      const { data, error } = await supabase
        .from('patient_questionnaire_responses' as any)
        .select('*')
        .eq('assignment_id', assignment.id);

      if (error) {
        console.error('Erro ao carregar respostas:', error);
        return;
      }

      setResponses((data as any) || []);
    } catch (error) {
      console.error('Erro ao carregar respostas:', error);
    }
  };

  const handleAnswerQuestionnaire = (assignment: Assignment) => {
    setSelectedAssignment(assignment);
    setIsViewingResponses(false);
    // Inicializar respostas vazias
    const initialResponses = assignment.questionnaire.questions?.map(q => ({
      question_id: q.id,
      response_text: ''
    })) || [];
    setResponses(initialResponses);
  };

  const handleResponseChange = (questionId: string, value: string) => {
    setResponses(prev => prev.map(r => 
      r.question_id === questionId ? { ...r, response_text: value } : r
    ));
  };

  const handleSubmitResponses = async () => {
    if (!selectedAssignment) return;
    
    try {
      // Inserir respostas
      const { error: responsesError } = await supabase
        .from('patient_questionnaire_responses' as any)
        .insert(
          responses.map(response => ({
            assignment_id: selectedAssignment.id,
            question_id: response.question_id,
            response_text: response.response_text
          }))
        );

      if (responsesError) {
        throw responsesError;
      }

      // Atualizar status do assignment para 'completed'
      const { error: assignmentError } = await supabase
        .from('patient_questionnaire_assignments' as any)
        .update({ 
          status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('id', selectedAssignment.id);

      if (assignmentError) {
        throw assignmentError;
      }
      
      setSelectedAssignment(null);
      setResponses([]);
      fetchAssignments(); // Refresh assignments
    } catch (error) {
      console.error('Erro ao enviar respostas:', error);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <FileQuestion className="w-5 h-5" />
          Questionários
        </h3>
        <PatientQuestionnaireAssignment 
          patientId={patientId}
          onAssignmentComplete={fetchAssignments}
        />
      </div>

      {assignments.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center text-muted-foreground">
            Nenhum questionário atribuído a este paciente.
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {assignments.map((assignment) => (
            <Card key={assignment.id}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">
                    {assignment.questionnaire.title}
                  </CardTitle>
                  <Badge variant={assignment.status === 'completed' ? 'default' : 'secondary'}>
                    {assignment.status === 'completed' ? (
                      <>
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Respondido
                      </>
                    ) : (
                      <>
                        <Clock className="w-3 h-3 mr-1" />
                        Pendente
                      </>
                    )}
                  </Badge>
                </div>
                {assignment.questionnaire.description && (
                  <p className="text-sm text-muted-foreground">
                    {assignment.questionnaire.description}
                  </p>
                )}
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    Atribuído em {format(new Date(assignment.assigned_at), 'dd/MM/yyyy', { locale: ptBR })}
                    {assignment.completed_at && (
                      <>
                        {' • '}
                        Respondido em {format(new Date(assignment.completed_at), 'dd/MM/yyyy', { locale: ptBR })}
                      </>
                    )}
                  </span>
                  <div className="flex gap-2">
                    {assignment.status === 'completed' ? (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewResponses(assignment)}
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        Ver Respostas
                      </Button>
                    ) : (
                      <Button
                        size="sm"
                        onClick={() => handleAnswerQuestionnaire(assignment)}
                      >
                        Responder
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Dialog para responder/visualizar questionário */}
      <Dialog open={!!selectedAssignment} onOpenChange={() => setSelectedAssignment(null)}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {isViewingResponses ? 'Respostas do Questionário' : 'Responder Questionário'}
              {selectedAssignment && ` - ${selectedAssignment.questionnaire.title}`}
            </DialogTitle>
          </DialogHeader>
          
          {selectedAssignment && (
            <div className="space-y-4">
              {selectedAssignment.questionnaire.questions
                ?.sort((a, b) => a.question_order - b.question_order)
                .map((question, index) => {
                  const response = responses.find(r => r.question_id === question.id);
                  return (
                    <div key={question.id} className="space-y-2">
                      <Label className="text-sm font-medium">
                        {index + 1}. {question.question_text}
                      </Label>
                      <Textarea
                        value={response?.response_text || ''}
                        onChange={(e) => handleResponseChange(question.id, e.target.value)}
                        placeholder="Digite sua resposta..."
                        disabled={isViewingResponses}
                        className="min-h-[80px]"
                      />
                    </div>
                  );
                })}
              
              {!isViewingResponses && (
                <div className="flex justify-end gap-2 pt-4">
                  <Button variant="outline" onClick={() => setSelectedAssignment(null)}>
                    Cancelar
                  </Button>
                  <Button onClick={handleSubmitResponses}>
                    Enviar Respostas
                  </Button>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}