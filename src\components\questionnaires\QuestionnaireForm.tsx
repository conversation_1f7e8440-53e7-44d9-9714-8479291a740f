import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Plus, Trash2, GripVertical } from "lucide-react";
import { useQuestionnaires, type Questionnaire } from "@/context/QuestionnairesContext";

interface QuestionnaireFormProps {
  questionnaire?: Questionnaire | null;
  onClose: () => void;
}

export function QuestionnaireForm({ questionnaire, onClose }: QuestionnaireFormProps) {
  const { addQuestionnaire, updateQuestionnaire, addQuestion, updateQuestion, deleteQuestion, fetchQuestionnaires } = useQuestionnaires();
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [questions, setQuestions] = useState<Array<{ id?: string; text: string; order: number; isNew?: boolean }>>([]);

  useEffect(() => {
    if (questionnaire) {
      setTitle(questionnaire.title);
      setDescription(questionnaire.description || "");
      setQuestions(questionnaire.questions?.map(q => ({
        id: q.id,
        text: q.question_text,
        order: q.question_order
      })) || []);
    } else {
      setTitle("");
      setDescription("");
      setQuestions([]);
    }
  }, [questionnaire]);

  const addNewQuestion = () => {
    const newOrder = questions.length + 1;
    setQuestions(prev => [...prev, { text: "", order: newOrder, isNew: true }]);
  };

  const updateQuestionText = (index: number, text: string) => {
    setQuestions(prev => prev.map((q, i) => i === index ? { ...q, text } : q));
  };

  const removeQuestion = async (index: number) => {
    const question = questions[index];
    if (question.id && !question.isNew) {
      await deleteQuestion(question.id);
    }
    setQuestions(prev => prev.filter((_, i) => i !== index).map((q, i) => ({ ...q, order: i + 1 })));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (questionnaire) {
        // Update existing questionnaire
        await updateQuestionnaire(questionnaire.id, { title, description });
        
        // Handle questions
        for (const question of questions) {
          if (question.isNew && question.text.trim()) {
            await addQuestion(questionnaire.id, question.text, question.order);
          } else if (question.id && !question.isNew) {
            await updateQuestion(question.id, question.text);
          }
        }
      } else {
        // Create new questionnaire
        const newQuestionnaire = await addQuestionnaire({ title, description });
        
        if (newQuestionnaire) {
          // Add questions to the new questionnaire
          for (const question of questions) {
            if (question.text.trim()) {
              await addQuestion(newQuestionnaire.id, question.text, question.order);
            }
          }
        }
      }
      
      onClose();
    } catch (error) {
      console.error('Erro ao salvar questionário:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div>
          <Label htmlFor="title">Título do Questionário</Label>
          <Input
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Ex: Avaliação Inicial"
            required
          />
        </div>
        
        <div>
          <Label htmlFor="description">Descrição (opcional)</Label>
          <Textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Descrição do questionário..."
            rows={3}
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Perguntas</CardTitle>
            <Button type="button" variant="outline" onClick={addNewQuestion}>
              <Plus className="w-4 h-4 mr-2" />
              Adicionar Pergunta
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {questions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              Nenhuma pergunta adicionada. Clique em "Adicionar Pergunta" para começar.
            </div>
          ) : (
            <div className="space-y-4">
              {questions.map((question, index) => (
                <div key={index} className="flex items-start gap-3 p-4 border rounded-lg">
                  <GripVertical className="w-5 h-5 text-muted-foreground mt-2 cursor-move" />
                  <div className="flex-1">
                    <Label htmlFor={`question-${index}`}>
                      Pergunta {index + 1}
                    </Label>
                    <Textarea
                      id={`question-${index}`}
                      value={question.text}
                      onChange={(e) => updateQuestionText(index, e.target.value)}
                      placeholder="Digite sua pergunta aqui..."
                      rows={2}
                      className="mt-1"
                    />
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeQuestion(index)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex justify-end gap-3">
        <Button type="button" variant="outline" onClick={onClose}>
          Cancelar
        </Button>
        <Button type="submit">
          {questionnaire ? "Atualizar" : "Criar"} Questionário
        </Button>
      </div>
    </form>
  );
}