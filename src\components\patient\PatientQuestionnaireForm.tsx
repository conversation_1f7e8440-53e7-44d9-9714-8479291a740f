import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSubmitQuestionnaireResponsesPatient } from "@/hooks/usePatientQuestionnaireView";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { ArrowLeft, Send, AlertCircle } from "lucide-react";
import { questionnaireResponseSchema } from "@/schemas/questionnaire";
import type {
  AssignmentWithQuestionnaire,
  QuestionnaireResponseFormData,
} from "@/schemas/questionnaire";

interface PatientQuestionnaireFormProps {
  assignment: AssignmentWithQuestionnaire;
  onBack: () => void;
  onCompleted: () => void;
}

export function PatientQuestionnaireForm({
  assignment,
  onBack,
  onCompleted,
}: PatientQuestionnaireFormProps) {
  const submitMutation = useSubmitQuestionnaireResponsesPatient();
  const questionnaire = assignment.questionnaire;

  const form = useForm<QuestionnaireResponseFormData>({
    resolver: zodResolver(questionnaireResponseSchema),
    defaultValues: {
      assignmentId: assignment.id,
      responses:
        questionnaire?.questions?.map((question) => ({
          questionId: question.id,
          responseText: "",
        })) || [],
    },
  });

  if (
    !questionnaire ||
    !questionnaire.questions ||
    questionnaire.questions.length === 0
  ) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Voltar
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Responder Questionário
            </h1>
            <p className="text-muted-foreground">
              {questionnaire?.title || "Questionário"}
            </p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Este questionário não possui perguntas válidas. Entre em contato com
            seu psicólogo.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const onSubmit = async (data: QuestionnaireResponseFormData) => {
    try {
      await submitMutation.mutateAsync(data);
      onCompleted();
    } catch (error) {
      console.error("Error submitting questionnaire:", error);
    }
  };

  const watchedResponses = form.watch("responses");
  const completedResponses = watchedResponses.filter(
    (r) => r.responseText.trim().length > 0
  ).length;
  const totalQuestions = questionnaire.questions.length;
  const progressPercentage = (completedResponses / totalQuestions) * 100;

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          onClick={onBack}
          disabled={submitMutation.isPending}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Voltar
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold tracking-tight">
            {questionnaire.title}
          </h1>
          <p className="text-muted-foreground">{questionnaire.description}</p>
        </div>
      </div>

      {/* Progress indicator */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progresso</span>
              <span>
                {completedResponses} de {totalQuestions} perguntas respondidas
              </span>
            </div>
            <Progress value={progressPercentage} className="w-full" />
          </div>
        </CardContent>
      </Card>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-6">
            {questionnaire.questions
              .sort((a, b) => a.question_order - b.question_order)
              .map((question, index) => (
                <Card key={question.id}>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <span className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm font-medium">
                        {index + 1}
                      </span>
                      Pergunta {index + 1}
                    </CardTitle>
                    <CardDescription className="text-base">
                      {question.question_text}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <FormField
                      control={form.control}
                      name={`responses.${index}.responseText`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="sr-only">
                            Resposta para: {question.question_text}
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Digite sua resposta aqui..."
                              className="min-h-[120px] resize-none"
                              disabled={submitMutation.isPending}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              ))}
          </div>

          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onBack}
                  disabled={submitMutation.isPending}
                  className="flex-1 sm:flex-none"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  disabled={
                    submitMutation.isPending || completedResponses === 0
                  }
                  className="flex-1"
                >
                  {submitMutation.isPending ? (
                    <>
                      <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                      Enviando...
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4 mr-2" />
                      Enviar Respostas ({completedResponses}/{totalQuestions})
                    </>
                  )}
                </Button>
              </div>

              {completedResponses === 0 && (
                <p className="text-sm text-muted-foreground mt-2">
                  Responda pelo menos uma pergunta para enviar o questionário.
                </p>
              )}

              {completedResponses > 0 &&
                completedResponses < totalQuestions && (
                  <p className="text-sm text-muted-foreground mt-2">
                    Você pode enviar o questionário mesmo sem responder todas as
                    perguntas.
                  </p>
                )}
            </CardContent>
          </Card>
        </form>
      </Form>
    </div>
  );
}
