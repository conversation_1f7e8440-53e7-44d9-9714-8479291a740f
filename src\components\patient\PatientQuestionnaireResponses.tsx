import { useQuestionnaireResponsesView } from "@/hooks/usePatientQuestionnaireView";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, FileText, CheckCircle, Calendar, AlertCircle } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import type { AssignmentWithQuestionnaire } from "@/schemas/questionnaire";

interface PatientQuestionnaireResponsesProps {
  assignment: AssignmentWithQuestionnaire;
  onBack: () => void;
}

export function PatientQuestionnaireResponses({ 
  assignment, 
  onBack 
}: PatientQuestionnaireResponsesProps) {
  const { data: responses, isLoading, error } = useQuestionnaireResponsesView(assignment.id);
  const questionnaire = assignment.questionnaire;

  if (!questionnaire || !questionnaire.questions) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Voltar
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Minhas Respostas</h1>
            <p className="text-muted-foreground">
              {questionnaire?.title || "Questionário"}
            </p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Questionário não encontrado ou sem perguntas válidas.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Voltar
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold tracking-tight">{questionnaire.title}</h1>
          <p className="text-muted-foreground">
            {questionnaire.description}
          </p>
        </div>
        <Badge variant="secondary" className="text-green-600 border-green-200">
          <CheckCircle className="w-3 h-3 mr-1" />
          Concluído
        </Badge>
      </div>

      {/* Assignment info */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2 text-muted-foreground" />
              <span className="text-muted-foreground">Atribuído em:</span>
              <span className="ml-2 font-medium">
                {format(new Date(assignment.assigned_at), "dd/MM/yyyy", { locale: ptBR })}
              </span>
            </div>
            
            {assignment.completed_at && (
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-muted-foreground" />
                <span className="text-muted-foreground">Concluído em:</span>
                <span className="ml-2 font-medium">
                  {format(new Date(assignment.completed_at), "dd/MM/yyyy", { locale: ptBR })}
                </span>
              </div>
            )}
            
            <div className="flex items-center">
              <FileText className="w-4 h-4 mr-2 text-muted-foreground" />
              <span className="text-muted-foreground">Total de perguntas:</span>
              <span className="ml-2 font-medium">{questionnaire.questions.length}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Loading state */}
      {isLoading && (
        <div className="space-y-6">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Error state */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Erro ao carregar suas respostas. Tente recarregar a página.
          </AlertDescription>
        </Alert>
      )}

      {/* Responses */}
      {!isLoading && !error && (
        <div className="space-y-6">
          {questionnaire.questions
            .sort((a, b) => a.question_order - b.question_order)
            .map((question, index) => {
              const response = responses?.find(r => r.question_id === question.id);
              
              return (
                <Card key={question.id}>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <span className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm font-medium">
                        {index + 1}
                      </span>
                      Pergunta {index + 1}
                    </CardTitle>
                    <CardDescription className="text-base">
                      {question.question_text}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="text-sm font-medium text-muted-foreground">
                        Sua resposta:
                      </div>
                      
                      {response ? (
                        <div className="bg-muted/50 rounded-lg p-4">
                          <p className="text-sm leading-relaxed whitespace-pre-wrap">
                            {response.response_text}
                          </p>
                          {response.created_at && (
                            <div className="mt-3 pt-3 border-t border-border">
                              <p className="text-xs text-muted-foreground">
                                Respondido em {format(new Date(response.created_at), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}
                              </p>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="bg-muted/30 rounded-lg p-4 border-2 border-dashed border-muted-foreground/20">
                          <p className="text-sm text-muted-foreground italic">
                            Esta pergunta não foi respondida.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}

          {/* Summary */}
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-2">
                <div className="flex items-center justify-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="font-medium">Questionário Concluído</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Você respondeu {responses?.length || 0} de {questionnaire.questions.length} perguntas
                </p>
                {assignment.completed_at && (
                  <p className="text-xs text-muted-foreground">
                    Enviado em {format(new Date(assignment.completed_at), "dd 'de' MMMM 'de' yyyy 'às' HH:mm", { locale: ptBR })}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
