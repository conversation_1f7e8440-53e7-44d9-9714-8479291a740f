import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "./AuthContext";

export interface Appointment {
  id: string;
  user_id: string;
  patient_id: string | null;
  title: string;
  description?: string;
  appointment_date: string;
  start_time: string;
  end_time: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'no_show';
  created_at: string;
  updated_at: string;
  patient?: {
    id: string;
    name: string;
    phone: string;
  };
}

export interface CreateAppointmentData {
  patient_id?: string | null;
  title: string;
  description?: string;
  appointment_date: string;
  start_time: string;
  end_time: string;
  status?: 'scheduled' | 'completed' | 'cancelled' | 'no_show';
}

interface AppointmentsContextType {
  appointments: Appointment[];
  loading: boolean;
  fetchAppointments: () => Promise<void>;
  addAppointment: (appointmentData: CreateAppointmentData) => Promise<void>;
  updateAppointment: (id: string, updates: Partial<CreateAppointmentData>) => Promise<void>;
  deleteAppointment: (id: string) => Promise<void>;
  getAppointmentsByDate: (date: string) => Appointment[];
}

const AppointmentsContext = createContext<AppointmentsContextType | undefined>(undefined);

export function AppointmentsProvider({ children }: { children: ReactNode }) {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  const fetchAppointments = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('appointments')
        .select(`
          *,
          patient:patients(id, name, phone)
        `)
        .eq('user_id', user.id)
        .order('appointment_date', { ascending: true })
        .order('start_time', { ascending: true });

      if (error) throw error;

      setAppointments((data || []) as Appointment[]);
    } catch (error) {
      console.error('Error fetching appointments:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const addAppointment = async (appointmentData: CreateAppointmentData) => {
    if (!user) throw new Error('User not authenticated');

    try {
      const { data, error } = await supabase
        .from('appointments')
        .insert([{
          ...appointmentData,
          user_id: user.id
        }])
        .select(`
          *,
          patient:patients(id, name, phone)
        `)
        .single();

      if (error) throw error;

      setAppointments(prev => [...prev, data as Appointment]);
    } catch (error) {
      console.error('Error adding appointment:', error);
      throw error;
    }
  };

  const updateAppointment = async (id: string, updates: Partial<CreateAppointmentData>) => {
    try {
      const { data, error } = await supabase
        .from('appointments')
        .update(updates)
        .eq('id', id)
        .select(`
          *,
          patient:patients(id, name, phone)
        `)
        .single();

      if (error) throw error;

      setAppointments(prev => 
        prev.map(appointment => 
          appointment.id === id ? data as Appointment : appointment
        )
      );
    } catch (error) {
      console.error('Error updating appointment:', error);
      throw error;
    }
  };

  const deleteAppointment = async (id: string) => {
    try {
      const { error } = await supabase
        .from('appointments')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setAppointments(prev => prev.filter(appointment => appointment.id !== id));
    } catch (error) {
      console.error('Error deleting appointment:', error);
      throw error;
    }
  };

  const getAppointmentsByDate = (date: string): Appointment[] => {
    return appointments.filter(appointment => appointment.appointment_date === date);
  };

  useEffect(() => {
    if (user) {
      fetchAppointments();
    }
  }, [user]);

  return (
    <AppointmentsContext.Provider
      value={{
        appointments,
        loading,
        fetchAppointments,
        addAppointment,
        updateAppointment,
        deleteAppointment,
        getAppointmentsByDate,
      }}
    >
      {children}
    </AppointmentsContext.Provider>
  );
}

export function useAppointments() {
  const context = useContext(AppointmentsContext);
  if (context === undefined) {
    throw new Error('useAppointments must be used within an AppointmentsProvider');
  }
  return context;
}