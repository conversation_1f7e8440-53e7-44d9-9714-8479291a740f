import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Calendar, Clock, User, Edit, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import type { Appointment } from "@/context/AppointmentsContext";
import { statusColors, statusLabels } from "../types";

interface DayViewProps {
  currentDate: Date;
  appointments: Appointment[];
  onEdit: (appointment: Appointment) => void;
  onDelete: (id: string) => void;
}

export function DayView({
  currentDate,
  appointments,
  onEdit,
  onDelete,
}: DayViewProps) {
  const getAppointmentsForDate = (date: Date) => {
    const dateStr = format(date, "yyyy-MM-dd");
    return appointments
      .filter((apt) => apt.appointment_date === dateStr)
      .sort((a, b) => a.start_time.localeCompare(b.start_time));
  };

  const dayAppointments = getAppointmentsForDate(currentDate);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calendar className="w-5 h-5" />
          <span>
            {format(currentDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {dayAppointments.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              Nenhum agendamento para este dia
            </p>
          </div>
        ) : (
          dayAppointments.map((appointment) => (
            <div
              key={appointment.id}
              className="flex items-center justify-between p-4 border rounded-lg"
            >
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <Clock className="w-4 h-4 text-muted-foreground" />
                  <span className="font-medium">
                    {appointment.start_time} - {appointment.end_time}
                  </span>
                  <Badge
                    variant="outline"
                    className={statusColors[appointment.status]}
                  >
                    {statusLabels[appointment.status]}
                  </Badge>
                </div>
                <h3 className="font-semibold">{appointment.title}</h3>
                {appointment.patient && (
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <User className="w-3 h-3" />
                    <span>{appointment.patient.name}</span>
                  </div>
                )}
                {appointment.description && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {appointment.description}
                  </p>
                )}
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit(appointment)}
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Excluir agendamento</AlertDialogTitle>
                      <AlertDialogDescription>
                        Tem certeza que deseja excluir este agendamento? Esta
                        ação não pode ser desfeita.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancelar</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => onDelete(appointment.id)}
                      >
                        Excluir
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  );
}
