import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Edit, Trash2, FileText } from "lucide-react";
import { useQuestionnaires } from "@/context/QuestionnairesContext";
import { QuestionnaireForm } from "@/components/questionnaires/QuestionnaireForm";

export function Questionnaires() {
  const { questionnaires, loading, deleteQuestionnaire } = useQuestionnaires();
  const [selectedQuestionnaire, setSelectedQuestionnaire] = useState<any>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleEdit = (questionnaire: any) => {
    setSelectedQuestionnaire(questionnaire);
    setDialogOpen(true);
  };

  const handleNew = () => {
    setSelectedQuestionnaire(null);
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedQuestionnaire(null);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-muted-foreground">Carregando questionários...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Questionários</h1>
          <p className="text-muted-foreground">
            Gerencie seus questionários pré-consulta
          </p>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleNew}>
              <Plus className="w-4 h-4 mr-2" />
              Novo Questionário
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {selectedQuestionnaire ? "Editar Questionário" : "Novo Questionário"}
              </DialogTitle>
            </DialogHeader>
            <QuestionnaireForm 
              questionnaire={selectedQuestionnaire}
              onClose={handleDialogClose}
            />
          </DialogContent>
        </Dialog>
      </div>

      {questionnaires.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Nenhum questionário encontrado</h3>
            <p className="text-muted-foreground text-center mb-4">
              Crie seu primeiro questionário para começar a coletar informações dos pacientes antes das consultas.
            </p>
            <Button onClick={handleNew}>
              <Plus className="w-4 h-4 mr-2" />
              Criar Primeiro Questionário
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {questionnaires.map((questionnaire) => (
            <Card key={questionnaire.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{questionnaire.title}</CardTitle>
                    {questionnaire.description && (
                      <CardDescription className="mt-2">
                        {questionnaire.description}
                      </CardDescription>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(questionnaire)}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteQuestionnaire(questionnaire.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-muted-foreground">
                  {questionnaire.questions?.length || 0} pergunta(s)
                </div>
                <div className="text-xs text-muted-foreground mt-2">
                  Criado em {new Date(questionnaire.created_at).toLocaleDateString('pt-BR')}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}