export type AppointmentStatus = 'scheduled' | 'completed' | 'cancelled' | 'no_show';

export type ViewMode = 'day' | 'week';

export interface AppointmentFormData {
  title: string;
  description: string;
  patient_id: string;
  appointment_date: string;
  start_time: string;
  end_time: string;
  status: AppointmentStatus;
}

export const statusColors = {
  scheduled: "bg-blue-500/10 text-blue-700 border-blue-200",
  completed: "bg-green-500/10 text-green-700 border-green-200",
  cancelled: "bg-red-500/10 text-red-700 border-red-200",
  no_show: "bg-gray-500/10 text-gray-700 border-gray-200",
} as const;

export const statusLabels = {
  scheduled: "Agendado",
  completed: "Concluído",
  cancelled: "Cancelado",
  no_show: "Fal<PERSON>",
} as const;
