// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://mzyrgkihwhkkrnutuazh.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im16eXJna2lod2hra3JudXR1YXpoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4MzMwNDUsImV4cCI6MjA2NzQwOTA0NX0.IfTtPaUrjAD5uiyP9S5C8TuRT7RdgoX063ygbh_0J-c";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});