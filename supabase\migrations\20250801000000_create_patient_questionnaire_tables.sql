-- Create table for patient questionnaire assignments
CREATE TABLE public.patient_questionnaire_assignments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  patient_id UUID NOT NULL REFERENCES public.patients(id) ON DELETE CASCADE,
  questionnaire_id UUID NOT NULL REFERENCES public.questionnaires(id) ON DELETE CASCADE,
  assigned_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed')),
  assigned_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create table for patient questionnaire responses
CREATE TABLE public.patient_questionnaire_responses (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  assignment_id UUID NOT NULL REFERENCES public.patient_questionnaire_assignments(id) ON DELETE CASCADE,
  question_id UUID NOT NULL REFERENCES public.questionnaire_questions(id) ON DELETE CASCADE,
  response_text TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.patient_questionnaire_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.patient_questionnaire_responses ENABLE ROW LEVEL SECURITY;

-- Create policies for patient_questionnaire_assignments
CREATE POLICY "Users can view assignments for their own patients" 
ON public.patient_questionnaire_assignments 
FOR SELECT 
USING (EXISTS (
  SELECT 1 FROM public.patients 
  WHERE patients.id = patient_questionnaire_assignments.patient_id 
  AND patients.user_id = auth.uid()
));

CREATE POLICY "Users can create assignments for their own patients" 
ON public.patient_questionnaire_assignments 
FOR INSERT 
WITH CHECK (EXISTS (
  SELECT 1 FROM public.patients 
  WHERE patients.id = patient_questionnaire_assignments.patient_id 
  AND patients.user_id = auth.uid()
) AND auth.uid() = assigned_by);

CREATE POLICY "Users can update assignments for their own patients" 
ON public.patient_questionnaire_assignments 
FOR UPDATE 
USING (EXISTS (
  SELECT 1 FROM public.patients 
  WHERE patients.id = patient_questionnaire_assignments.patient_id 
  AND patients.user_id = auth.uid()
));

CREATE POLICY "Users can delete assignments for their own patients" 
ON public.patient_questionnaire_assignments 
FOR DELETE 
USING (EXISTS (
  SELECT 1 FROM public.patients 
  WHERE patients.id = patient_questionnaire_assignments.patient_id 
  AND patients.user_id = auth.uid()
));

-- Create policies for patient_questionnaire_responses
CREATE POLICY "Users can view responses for their own patient assignments" 
ON public.patient_questionnaire_responses 
FOR SELECT 
USING (EXISTS (
  SELECT 1 FROM public.patient_questionnaire_assignments pqa
  JOIN public.patients p ON p.id = pqa.patient_id
  WHERE pqa.id = patient_questionnaire_responses.assignment_id 
  AND p.user_id = auth.uid()
));

CREATE POLICY "Users can create responses for their own patient assignments" 
ON public.patient_questionnaire_responses 
FOR INSERT 
WITH CHECK (EXISTS (
  SELECT 1 FROM public.patient_questionnaire_assignments pqa
  JOIN public.patients p ON p.id = pqa.patient_id
  WHERE pqa.id = patient_questionnaire_responses.assignment_id 
  AND p.user_id = auth.uid()
));

CREATE POLICY "Users can update responses for their own patient assignments" 
ON public.patient_questionnaire_responses 
FOR UPDATE 
USING (EXISTS (
  SELECT 1 FROM public.patient_questionnaire_assignments pqa
  JOIN public.patients p ON p.id = pqa.patient_id
  WHERE pqa.id = patient_questionnaire_responses.assignment_id 
  AND p.user_id = auth.uid()
));

CREATE POLICY "Users can delete responses for their own patient assignments" 
ON public.patient_questionnaire_responses 
FOR DELETE 
USING (EXISTS (
  SELECT 1 FROM public.patient_questionnaire_assignments pqa
  JOIN public.patients p ON p.id = pqa.patient_id
  WHERE pqa.id = patient_questionnaire_responses.assignment_id 
  AND p.user_id = auth.uid()
));

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_patient_questionnaire_assignments_updated_at
BEFORE UPDATE ON public.patient_questionnaire_assignments
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_patient_questionnaire_responses_updated_at
BEFORE UPDATE ON public.patient_questionnaire_responses
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX idx_patient_questionnaire_assignments_patient_id ON public.patient_questionnaire_assignments(patient_id);
CREATE INDEX idx_patient_questionnaire_assignments_questionnaire_id ON public.patient_questionnaire_assignments(questionnaire_id);
CREATE INDEX idx_patient_questionnaire_assignments_assigned_by ON public.patient_questionnaire_assignments(assigned_by);
CREATE INDEX idx_patient_questionnaire_assignments_status ON public.patient_questionnaire_assignments(status);
CREATE INDEX idx_patient_questionnaire_responses_assignment_id ON public.patient_questionnaire_responses(assignment_id);
CREATE INDEX idx_patient_questionnaire_responses_question_id ON public.patient_questionnaire_responses(question_id);

-- Add unique constraint to prevent duplicate assignments
CREATE UNIQUE INDEX idx_unique_patient_questionnaire_assignment 
ON public.patient_questionnaire_assignments(patient_id, questionnaire_id) 
WHERE status = 'pending';

-- Add unique constraint to prevent duplicate responses for the same question in an assignment
CREATE UNIQUE INDEX idx_unique_assignment_question_response 
ON public.patient_questionnaire_responses(assignment_id, question_id);
