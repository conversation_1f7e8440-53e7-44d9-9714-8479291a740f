import { useState } from "react";
import { usePatients, ConsultationSummary } from "@/context/PatientsContext";
import { useAppointments } from "@/context/AppointmentsContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { PatientQuestionnairesList } from "./PatientQuestionnairesList";
import {
  ArrowLeft,
  Edit,
  Plus,
  Calendar,
  FileText,
  Upload,
  X,
  Trash2,
  Clock,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";

interface PatientDetailsProps {
  patientId: string;
  onClose: () => void;
  onEdit: () => void;
}

export function PatientDetails({
  patientId,
  onClose,
  onEdit,
}: PatientDetailsProps) {
  const { getPatient, addConsultation, updateConsultation, deleteConsultation } = usePatients();
  const { appointments, updateAppointment, deleteAppointment } = useAppointments();
  const { toast } = useToast();
  const patient = getPatient(patientId);

  const [showAddConsultation, setShowAddConsultation] = useState(false);
  const [showEditConsultation, setShowEditConsultation] = useState(false);
  const [editingConsultation, setEditingConsultation] = useState<ConsultationSummary | null>(null);
  const [consultationSummary, setConsultationSummary] = useState("");
  const [consultationDate, setConsultationDate] = useState(
    new Date().toISOString().split("T")[0]
  );
  const [audioFile, setAudioFile] = useState<File | null>(null);
  if (!patient) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onClose}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Voltar
          </Button>
        </div>
        <Card>
          <CardContent className="py-12 text-center">
            <p className="text-muted-foreground">Paciente não encontrado</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleAddConsultation = async () => {
    if (!consultationSummary.trim()) {
      toast({
        title: "Erro",
        description: "Digite o resumo da consulta",
        variant: "destructive",
      });
      return;
    }

    await addConsultation(patientId, {
      date: new Date(consultationDate),
      summary: consultationSummary,
    });

    setConsultationSummary("");
    setConsultationDate(new Date().toISOString().split("T")[0]);
    setAudioFile(null);
    setShowAddConsultation(false);
  };

  const handleEditConsultation = (consultation: ConsultationSummary) => {
    setEditingConsultation(consultation);
    setConsultationSummary(consultation.summary);
    setConsultationDate(consultation.date.toISOString().split("T")[0]);
    setShowEditConsultation(true);
  };

  const handleUpdateConsultation = async () => {
    if (!editingConsultation || !consultationSummary.trim()) {
      toast({
        title: "Erro",
        description: "Digite o resumo da consulta",
        variant: "destructive",
      });
      return;
    }

    await updateConsultation(editingConsultation.id, {
      date: new Date(consultationDate),
      summary: consultationSummary,
    });

    setConsultationSummary("");
    setConsultationDate(new Date().toISOString().split("T")[0]);
    setAudioFile(null);
    setEditingConsultation(null);
    setShowEditConsultation(false);
  };

  const handleDeleteConsultation = async (consultationId: string) => {
    await deleteConsultation(consultationId);
  };

  const sortedConsultations = [...patient.consultationSummaries].sort(
    (a, b) => b.date.getTime() - a.date.getTime()
  );

  // Get upcoming appointments for this patient
  const upcomingAppointments = appointments
    .filter(
      (appointment) =>
        appointment.patient_id === patientId &&
        appointment.status === "scheduled" &&
        new Date(`${appointment.appointment_date}T${appointment.start_time}`) >
          new Date()
    )
    .sort(
      (a, b) =>
        new Date(`${a.appointment_date}T${a.start_time}`).getTime() -
        new Date(`${b.appointment_date}T${b.start_time}`).getTime()
    );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onClose}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Voltar
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              {patient.name}
            </h1>
            <p className="text-muted-foreground">Detalhes do paciente</p>
          </div>
        </div>
        <Button onClick={onEdit} className="bg-primary hover:bg-primary-hover">
          <Edit className="w-4 h-4 mr-2" />
          Editar
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Patient Info */}
        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="w-5 h-5" />
                <span>Informações Pessoais</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  CPF
                </Label>
                <p className="text-foreground">{patient.cpf}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Telefone
                </Label>
                <p className="text-foreground">{patient.phone}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Cadastrado em
                </Label>
                <p className="text-foreground">
                  {patient.createdAt.toLocaleDateString("pt-BR")}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Última atualização
                </Label>
                <p className="text-foreground">
                  {patient.updatedAt.toLocaleDateString("pt-BR")}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Queixas Principais</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-foreground whitespace-pre-wrap">
                {patient.mainComplaints || "Não informado"}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Anamnese</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-foreground whitespace-pre-wrap">
                {patient.anamnesis || "Não informado"}
              </p>
            </CardContent>
          </Card>

          {upcomingAppointments.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="w-5 h-5" />
                  <span>Próximas Consultas</span>
                  <Badge variant="secondary">
                    {upcomingAppointments.length}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {upcomingAppointments.map((appointment) => (
                  <div
                    key={appointment.id}
                    className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg"
                  >
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <div className="flex-1">
                      <p className="font-medium text-foreground">
                        {appointment.title}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(
                          appointment.appointment_date
                        ).toLocaleDateString("pt-BR")}{" "}
                        às {appointment.start_time}
                      </p>
                      {appointment.description && (
                        <p className="text-sm text-muted-foreground mt-1">
                          {appointment.description}
                        </p>
                      )}
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {appointment.status === "scheduled"
                        ? "Agendada"
                        : appointment.status}
                    </Badge>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Consultations and Questionnaires */}
        <div className="lg:col-span-2 space-y-6">
          {/* Questionnaires Section */}
          <PatientQuestionnairesList patientId={patientId} />
          
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="w-5 h-5" />
                  <span>Consultas</span>
                  <Badge variant="secondary">
                    {patient.consultationSummaries.length}
                  </Badge>
                </CardTitle>
                <Dialog
                  open={showAddConsultation}
                  onOpenChange={setShowAddConsultation}
                >
                  <DialogTrigger asChild>
                    <Button
                      size="sm"
                      className="bg-secondary hover:bg-secondary-hover"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Nova Consulta
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Adicionar Consulta</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="consultationDate">
                          Data da Consulta
                        </Label>
                        <input
                          type="date"
                          id="consultationDate"
                          value={consultationDate}
                          onChange={(e) => setConsultationDate(e.target.value)}
                          className="w-full px-3 py-2 border border-input rounded-md bg-background"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="audioUpload">Áudio da Consulta</Label>
                        <div className="border-2 border-dashed border-border rounded-lg p-6 bg-muted/50">
                          {!audioFile ? (
                            <div className="text-center">
                              <Upload className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                              <p className="text-sm text-muted-foreground mb-2">
                                Arraste um arquivo de áudio ou clique para
                                selecionar
                              </p>
                              <input
                                type="file"
                                id="audioUpload"
                                accept="audio/*"
                                onChange={(e) => {
                                  const file = e.target.files?.[0];
                                  if (file) setAudioFile(file);
                                }}
                                className="hidden"
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  document
                                    .getElementById("audioUpload")
                                    ?.click()
                                }
                              >
                                Selecionar Arquivo
                              </Button>
                            </div>
                          ) : (
                            <>
                              <div className="flex items-center justify-between p-3 bg-background rounded border">
                                <div className="flex items-center space-x-3">
                                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                    <FileText className="w-4 h-4 text-primary" />
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium">
                                      {audioFile.name}
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                      {(audioFile.size / 1024 / 1024).toFixed(
                                        2
                                      )}{" "}
                                      MB
                                    </p>
                                  </div>
                                </div>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setAudioFile(null)}
                                >
                                  <X className="w-4 h-4" />
                                </Button>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="consultationSummary">
                          Resumo da Consulta
                        </Label>
                        <Textarea
                          id="consultationSummary"
                          value={consultationSummary}
                          onChange={(e) =>
                            setConsultationSummary(e.target.value)
                          }
                          placeholder="Descreva o que foi abordado na consulta, técnicas utilizadas, progressos observados..."
                          className="min-h-[150px]"
                        />
                      </div>
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          onClick={() => setShowAddConsultation(false)}
                        >
                          Cancelar
                        </Button>
                        <Button onClick={handleAddConsultation}>
                          Salvar Consulta
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              {sortedConsultations.length === 0 ? (
                <div className="text-center py-12">
                  <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground mb-4">
                    Nenhuma consulta registrada ainda
                  </p>
                  <Button
                    onClick={() => setShowAddConsultation(true)}
                    variant="outline"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Adicionar primeira consulta
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {sortedConsultations.map((consultation, index) => (
                    <div key={consultation.id}>
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                            <Calendar className="w-5 h-5 text-primary" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <p className="font-medium text-foreground">
                                {consultation.date.toLocaleDateString("pt-BR", {
                                  weekday: "long",
                                  year: "numeric",
                                  month: "long",
                                  day: "numeric",
                                })}
                              </p>
                              <Badge variant="outline" className="text-xs">
                                Consulta {sortedConsultations.length - index}
                              </Badge>
                            </div>
                            <div className="flex space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => handleEditConsultation(consultation)}
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>
                                      Excluir consulta
                                    </AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Tem certeza que deseja excluir esta consulta?
                                      Esta ação não pode ser desfeita.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => handleDeleteConsultation(consultation.id)}
                                    >
                                      Excluir
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </div>
                          </div>
                          <p className="text-foreground whitespace-pre-wrap">
                            {consultation.summary}
                          </p>
                        </div>
                      </div>
                      {index < sortedConsultations.length - 1 && (
                        <Separator className="my-6" />
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Consultation Dialog */}
      <Dialog open={showEditConsultation} onOpenChange={setShowEditConsultation}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Editar Consulta</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="editConsultationDate">Data da Consulta</Label>
              <input
                type="date"
                id="editConsultationDate"
                value={consultationDate}
                onChange={(e) => setConsultationDate(e.target.value)}
                className="w-full px-3 py-2 border border-input rounded-md bg-background"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="editConsultationSummary">Resumo da Consulta</Label>
              <Textarea
                id="editConsultationSummary"
                value={consultationSummary}
                onChange={(e) => setConsultationSummary(e.target.value)}
                placeholder="Descreva o que foi abordado na consulta, técnicas utilizadas, progressos observados..."
                className="min-h-[150px]"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowEditConsultation(false);
                  setEditingConsultation(null);
                  setConsultationSummary("");
                  setConsultationDate(new Date().toISOString().split("T")[0]);
                }}
              >
                Cancelar
              </Button>
              <Button onClick={handleUpdateConsultation}>
                Atualizar Consulta
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
