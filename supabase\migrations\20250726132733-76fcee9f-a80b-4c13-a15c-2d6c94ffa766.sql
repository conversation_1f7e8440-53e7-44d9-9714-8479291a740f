-- Create table for questionnaires
CREATE TABLE public.questionnaires (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create table for questionnaire questions
CREATE TABLE public.questionnaire_questions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  questionnaire_id UUID NOT NULL REFERENCES public.questionnaires(id) ON DELETE CASCADE,
  question_text TEXT NOT NULL,
  question_order INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.questionnaires ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.questionnaire_questions ENABLE ROW LEVEL SECURITY;

-- Create policies for questionnaires
CREATE POLICY "Users can view their own questionnaires" 
ON public.questionnaires 
FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own questionnaires" 
ON public.questionnaires 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own questionnaires" 
ON public.questionnaires 
FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own questionnaires" 
ON public.questionnaires 
FOR DELETE 
USING (auth.uid() = user_id);

-- Create policies for questionnaire questions
CREATE POLICY "Users can view questions from their own questionnaires" 
ON public.questionnaire_questions 
FOR SELECT 
USING (EXISTS (
  SELECT 1 FROM public.questionnaires 
  WHERE questionnaires.id = questionnaire_questions.questionnaire_id 
  AND questionnaires.user_id = auth.uid()
));

CREATE POLICY "Users can create questions for their own questionnaires" 
ON public.questionnaire_questions 
FOR INSERT 
WITH CHECK (EXISTS (
  SELECT 1 FROM public.questionnaires 
  WHERE questionnaires.id = questionnaire_questions.questionnaire_id 
  AND questionnaires.user_id = auth.uid()
));

CREATE POLICY "Users can update questions from their own questionnaires" 
ON public.questionnaire_questions 
FOR UPDATE 
USING (EXISTS (
  SELECT 1 FROM public.questionnaires 
  WHERE questionnaires.id = questionnaire_questions.questionnaire_id 
  AND questionnaires.user_id = auth.uid()
));

CREATE POLICY "Users can delete questions from their own questionnaires" 
ON public.questionnaire_questions 
FOR DELETE 
USING (EXISTS (
  SELECT 1 FROM public.questionnaires 
  WHERE questionnaires.id = questionnaire_questions.questionnaire_id 
  AND questionnaires.user_id = auth.uid()
));

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_questionnaires_updated_at
BEFORE UPDATE ON public.questionnaires
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();