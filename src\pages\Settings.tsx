import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { Settings as SettingsIcon, User, Building2, LogOut } from 'lucide-react';

const Settings = () => {
  const { clinicInfo, updateClinicInfo, logout } = useAuth();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState({
    doctorName: clinicInfo.doctorName,
    clinicName: clinicInfo.clinicName,
    crp: clinicInfo.crp,
    phone: clinicInfo.phone,
    email: clinicInfo.email,
  });

  // Sync form data when clinic info loads from database
  useEffect(() => {
    setFormData({
      doctorName: clinicInfo.doctorName,
      clinicName: clinicInfo.clinicName,
      crp: clinicInfo.crp,
      phone: clinicInfo.phone,
      email: clinicInfo.email,
    });
  }, [clinicInfo]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    const { error } = await updateClinicInfo(formData);
    
    if (error) {
      toast({
        title: "Erro ao salvar",
        description: error,
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Configurações salvas!",
      description: "As informações foram atualizadas com sucesso.",
    });
  };

  const handleLogout = () => {
    logout();
    toast({
      title: "Logout realizado",
      description: "Você foi desconectado com sucesso.",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-3">
        <SettingsIcon className="w-8 h-8 text-primary" />
        <div>
          <h1 className="text-3xl font-bold text-foreground">Configurações</h1>
          <p className="text-muted-foreground">Gerencie as informações do sistema</p>
        </div>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="w-5 h-5" />
              <span>Informações do Psicólogo</span>
            </CardTitle>
            <CardDescription>
              Configure suas informações profissionais
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="doctorName">Nome do Psicólogo</Label>
                <Input
                  id="doctorName"
                  value={formData.doctorName}
                  onChange={(e) => handleInputChange('doctorName', e.target.value)}
                  placeholder="Dr. João Silva"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="crp">CRP</Label>
                <Input
                  id="crp"
                  value={formData.crp}
                  onChange={(e) => handleInputChange('crp', e.target.value)}
                  placeholder="CRP 06/12345"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Telefone</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="(11) 99999-9999"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building2 className="w-5 h-5" />
              <span>Informações da Clínica</span>
            </CardTitle>
            <CardDescription>
              Configure as informações da sua clínica
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="clinicName">Nome da Clínica</Label>
              <Input
                id="clinicName"
                value={formData.clinicName}
                onChange={(e) => handleInputChange('clinicName', e.target.value)}
                placeholder="PsicoSys"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-3 justify-between">
              <Button 
                onClick={handleSave}
                className="flex items-center space-x-2"
              >
                <SettingsIcon className="w-4 h-4" />
                <span>Salvar Configurações</span>
              </Button>
              
              <Separator className="sm:hidden" />
              
              <Button 
                variant="destructive"
                onClick={handleLogout}
                className="flex items-center space-x-2"
              >
                <LogOut className="w-4 h-4" />
                <span>Sair do Sistema</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Settings;