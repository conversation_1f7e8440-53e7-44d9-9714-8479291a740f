import { z } from 'zod';

// Schema for questionnaire assignment form
export const questionnaireAssignmentSchema = z.object({
  questionnaireId: z.string().min(1, 'Selecione um questionário'),
  patientId: z.string().min(1, 'ID do paciente é obrigatório'),
});

export type QuestionnaireAssignmentFormData = z.infer<typeof questionnaireAssignmentSchema>;

// Schema for individual questionnaire response
export const questionnaireResponseSchema = z.object({
  questionId: z.string().min(1, 'ID da pergunta é obrigatório'),
  responseText: z.string().min(1, 'Resposta é obrigatória').max(2000, 'Resposta muito longa'),
});

// Schema for complete questionnaire response form
export const questionnaireResponseFormSchema = z.object({
  assignmentId: z.string().min(1, 'ID da atribuição é obrigatório'),
  responses: z.array(questionnaireResponseSchema).min(1, 'Pelo menos uma resposta é obrigatória'),
});

export type QuestionnaireResponseFormData = z.infer<typeof questionnaireResponseFormSchema>;
export type QuestionnaireResponse = z.infer<typeof questionnaireResponseSchema>;

// Schema for questionnaire assignment data from database
export const assignmentSchema = z.object({
  id: z.string(),
  patient_id: z.string(),
  questionnaire_id: z.string(),
  assigned_by: z.string(),
  status: z.enum(['pending', 'completed']),
  assigned_at: z.string(),
  completed_at: z.string().nullable(),
  created_at: z.string(),
  updated_at: z.string(),
});

export type Assignment = z.infer<typeof assignmentSchema>;

// Schema for questionnaire response data from database
export const responseSchema = z.object({
  id: z.string(),
  assignment_id: z.string(),
  question_id: z.string(),
  response_text: z.string(),
  created_at: z.string(),
  updated_at: z.string(),
});

export type Response = z.infer<typeof responseSchema>;

// Schema for questionnaire data
export const questionSchema = z.object({
  id: z.string(),
  question_text: z.string(),
  question_order: z.number(),
  questionnaire_id: z.string(),
  created_at: z.string(),
});

export const questionnaireSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().nullable(),
  user_id: z.string(),
  created_at: z.string(),
  updated_at: z.string(),
  questions: z.array(questionSchema).optional(),
});

export type Question = z.infer<typeof questionSchema>;
export type Questionnaire = z.infer<typeof questionnaireSchema>;

// Schema for assignment with populated questionnaire data
export const assignmentWithQuestionnaireSchema = z.object({
  id: z.string(),
  patient_id: z.string(),
  questionnaire_id: z.string(),
  assigned_by: z.string(),
  status: z.enum(['pending', 'completed']),
  assigned_at: z.string(),
  completed_at: z.string().nullable(),
  created_at: z.string(),
  updated_at: z.string(),
  questionnaire: questionnaireSchema,
});

export type AssignmentWithQuestionnaire = z.infer<typeof assignmentWithQuestionnaireSchema>;

// Validation helpers
export const validateQuestionnaireAssignment = (data: unknown): QuestionnaireAssignmentFormData => {
  return questionnaireAssignmentSchema.parse(data);
};

export const validateQuestionnaireResponse = (data: unknown): QuestionnaireResponseFormData => {
  return questionnaireResponseFormSchema.parse(data);
};

// Form field validation schemas for individual fields
export const questionnaireIdSchema = z.string().min(1, 'Selecione um questionário');
export const responseTextSchema = z.string().min(1, 'Resposta é obrigatória').max(2000, 'Resposta muito longa');
