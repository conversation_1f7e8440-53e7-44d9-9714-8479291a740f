export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)";
  };
  public: {
    Tables: {
      appointments: {
        Row: {
          appointment_date: string;
          created_at: string;
          description: string | null;
          end_time: string;
          id: string;
          patient_id: string | null;
          start_time: string;
          status: string;
          title: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          appointment_date: string;
          created_at?: string;
          description?: string | null;
          end_time: string;
          id?: string;
          patient_id?: string | null;
          start_time: string;
          status?: string;
          title: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          appointment_date?: string;
          created_at?: string;
          description?: string | null;
          end_time?: string;
          id?: string;
          patient_id?: string | null;
          start_time?: string;
          status?: string;
          title?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "appointments_patient_id_fkey";
            columns: ["patient_id"];
            isOneToOne: false;
            referencedRelation: "patients";
            referencedColumns: ["id"];
          }
        ];
      };
      consultation_summaries: {
        Row: {
          created_at: string;
          date: string;
          id: string;
          patient_id: string;
          summary: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          date: string;
          id?: string;
          patient_id: string;
          summary: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          date?: string;
          id?: string;
          patient_id?: string;
          summary?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "consultation_summaries_patient_id_fkey";
            columns: ["patient_id"];
            isOneToOne: false;
            referencedRelation: "patients";
            referencedColumns: ["id"];
          }
        ];
      };
      patient_questionnaire_assignments: {
        Row: {
          assigned_at: string;
          assigned_by: string;
          completed_at: string | null;
          created_at: string;
          id: string;
          patient_id: string;
          questionnaire_id: string;
          status: string;
          updated_at: string;
        };
        Insert: {
          assigned_at?: string;
          assigned_by: string;
          completed_at?: string | null;
          created_at?: string;
          id?: string;
          patient_id: string;
          questionnaire_id: string;
          status?: string;
          updated_at?: string;
        };
        Update: {
          assigned_at?: string;
          assigned_by?: string;
          completed_at?: string | null;
          created_at?: string;
          id?: string;
          patient_id?: string;
          questionnaire_id?: string;
          status?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "patient_questionnaire_assignments_assigned_by_fkey";
            columns: ["assigned_by"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "patient_questionnaire_assignments_patient_id_fkey";
            columns: ["patient_id"];
            isOneToOne: false;
            referencedRelation: "patients";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "patient_questionnaire_assignments_questionnaire_id_fkey";
            columns: ["questionnaire_id"];
            isOneToOne: false;
            referencedRelation: "questionnaires";
            referencedColumns: ["id"];
          }
        ];
      };
      patient_questionnaire_responses: {
        Row: {
          assignment_id: string;
          created_at: string;
          id: string;
          question_id: string;
          response_text: string;
          updated_at: string;
        };
        Insert: {
          assignment_id: string;
          created_at?: string;
          id?: string;
          question_id: string;
          response_text: string;
          updated_at?: string;
        };
        Update: {
          assignment_id?: string;
          created_at?: string;
          id?: string;
          question_id?: string;
          response_text?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "patient_questionnaire_responses_assignment_id_fkey";
            columns: ["assignment_id"];
            isOneToOne: false;
            referencedRelation: "patient_questionnaire_assignments";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "patient_questionnaire_responses_question_id_fkey";
            columns: ["question_id"];
            isOneToOne: false;
            referencedRelation: "questionnaire_questions";
            referencedColumns: ["id"];
          }
        ];
      };
      patients: {
        Row: {
          anamnesis: string | null;
          cpf: string;
          created_at: string;
          id: string;
          main_complaints: string | null;
          name: string;
          phone: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          anamnesis?: string | null;
          cpf: string;
          created_at?: string;
          id?: string;
          main_complaints?: string | null;
          name: string;
          phone: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          anamnesis?: string | null;
          cpf?: string;
          created_at?: string;
          id?: string;
          main_complaints?: string | null;
          name?: string;
          phone?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [];
      };
      profiles: {
        Row: {
          clinic_name: string;
          created_at: string;
          crp: string;
          doctor_name: string;
          email: string;
          id: string;
          phone: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          clinic_name?: string;
          created_at?: string;
          crp?: string;
          doctor_name?: string;
          email: string;
          id?: string;
          phone?: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          clinic_name?: string;
          created_at?: string;
          crp?: string;
          doctor_name?: string;
          email?: string;
          id?: string;
          phone?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [];
      };
      questionnaire_questions: {
        Row: {
          created_at: string;
          id: string;
          question_order: number;
          question_text: string;
          questionnaire_id: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          question_order?: number;
          question_text: string;
          questionnaire_id: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          question_order?: number;
          question_text?: string;
          questionnaire_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "questionnaire_questions_questionnaire_id_fkey";
            columns: ["questionnaire_id"];
            isOneToOne: false;
            referencedRelation: "questionnaires";
            referencedColumns: ["id"];
          }
        ];
      };
      questionnaires: {
        Row: {
          created_at: string;
          description: string | null;
          id: string;
          title: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          description?: string | null;
          id?: string;
          title: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          description?: string | null;
          id?: string;
          title?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">;

type DefaultSchema = DatabaseWithoutInternals[Extract<
  keyof Database,
  "public"
>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
      DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
      DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  public: {
    Enums: {},
  },
} as const;
