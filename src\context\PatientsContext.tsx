import React, { createContext, useContext, ReactNode } from "react";
import {
  usePatientsQuery,
  useCreatePatient,
  useUpdatePatient,
  useDeletePatient,
  useCreateConsultation,
  useUpdateConsultation,
  useDeleteConsultation,
} from "@/hooks/usePatients";
import type {
  Patient,
  ConsultationSummary,
  CreatePatientData,
  UpdatePatientData,
  CreateConsultationData,
  UpdateConsultationData,
} from "@/http/patients";

// Re-export types for backward compatibility
export type { Patient, ConsultationSummary };

interface PatientsContextType {
  patients: Patient[];
  loading: boolean;
  error: Error | null;
  addPatient: (patient: CreatePatientData) => Promise<void>;
  updatePatient: (id: string, patient: UpdatePatientData) => Promise<void>;
  deletePatient: (id: string) => Promise<void>;
  getPatient: (id: string) => Patient | undefined;
  addConsultation: (
    patientId: string,
    consultation: CreateConsultationData
  ) => Promise<void>;
  updateConsultation: (
    consultationId: string,
    updates: UpdateConsultationData
  ) => Promise<void>;
  deleteConsultation: (consultationId: string) => Promise<void>;
  refreshPatients: () => Promise<void>;
}

const PatientsContext = createContext<PatientsContextType | undefined>(
  undefined
);

export function PatientsProvider({ children }: { children: ReactNode }) {
  // React Query hooks
  const patientsQuery = usePatientsQuery();
  const createPatientMutation = useCreatePatient();
  const updatePatientMutation = useUpdatePatient();
  const deletePatientMutation = useDeletePatient();
  const createConsultationMutation = useCreateConsultation();
  const updateConsultationMutation = useUpdateConsultation();
  const deleteConsultationMutation = useDeleteConsultation();

  // Wrapper functions to maintain backward compatibility
  const addPatient = async (patientData: CreatePatientData) => {
    await createPatientMutation.mutateAsync(patientData);
  };

  const updatePatient = async (id: string, updates: UpdatePatientData) => {
    await updatePatientMutation.mutateAsync({ id, updates });
  };

  const deletePatient = async (id: string) => {
    await deletePatientMutation.mutateAsync(id);
  };

  const getPatient = (id: string) => {
    return patientsQuery.data?.find((patient) => patient.id === id);
  };

  const addConsultation = async (
    patientId: string,
    consultationData: CreateConsultationData
  ) => {
    await createConsultationMutation.mutateAsync({
      patientId,
      consultationData,
    });
  };

  const updateConsultation = async (
    consultationId: string,
    updates: UpdateConsultationData
  ) => {
    await updateConsultationMutation.mutateAsync({ consultationId, updates });
  };

  const deleteConsultation = async (consultationId: string) => {
    await deleteConsultationMutation.mutateAsync(consultationId);
  };

  const refreshPatients = async () => {
    await patientsQuery.refetch();
  };

  return (
    <PatientsContext.Provider
      value={{
        patients: patientsQuery.data || [],
        loading: patientsQuery.isLoading,
        error: patientsQuery.error,
        addPatient,
        updatePatient,
        deletePatient,
        getPatient,
        addConsultation,
        updateConsultation,
        deleteConsultation,
        refreshPatients,
      }}
    >
      {children}
    </PatientsContext.Provider>
  );
}

export function usePatients() {
  const context = useContext(PatientsContext);
  if (context === undefined) {
    throw new Error("usePatients must be used within a PatientsProvider");
  }
  return context;
}
