/* eslint-disable @typescript-eslint/no-explicit-any */
import { supabase } from "@/integrations/supabase/client";
import type { 
  AssignmentWithQuestionnaire, 
  Response, 
  QuestionnaireAssignmentFormData,
  QuestionnaireResponseFormData 
} from "@/schemas/questionnaire";

/**
 * Fetch patient questionnaire assignments with questionnaire details
 */
export async function getPatientQuestionnaireAssignments(
  patientId: string
): Promise<AssignmentWithQuestionnaire[]> {
  const { data, error } = await supabase
    .from('patient_questionnaire_assignments')
    .select(`
      *,
      questionnaire:questionnaires(
        id,
        title,
        description,
        user_id,
        created_at,
        updated_at,
        questions:questionnaire_questions(
          id,
          question_text,
          question_order,
          questionnaire_id,
          created_at
        )
      )
    `)
    .eq('patient_id', patientId)
    .order('assigned_at', { ascending: false });

  if (error) {
    console.error('Error fetching patient questionnaire assignments:', error);
    throw error;
  }

  return (data || []) as AssignmentWithQuestionnaire[];
}

/**
 * Fetch questionnaire responses for a specific assignment
 */
export async function getQuestionnaireResponses(
  assignmentId: string
): Promise<Response[]> {
  const { data, error } = await supabase
    .from('patient_questionnaire_responses')
    .select('*')
    .eq('assignment_id', assignmentId)
    .order('created_at', { ascending: true });

  if (error) {
    console.error('Error fetching questionnaire responses:', error);
    throw error;
  }

  return (data || []) as Response[];
}

/**
 * Assign a questionnaire to a patient
 */
export async function assignQuestionnaireToPatient(
  data: QuestionnaireAssignmentFormData,
  assignedBy: string
): Promise<void> {
  const { error } = await supabase
    .from('patient_questionnaire_assignments')
    .insert({
      patient_id: data.patientId,
      questionnaire_id: data.questionnaireId,
      assigned_by: assignedBy,
      status: 'pending',
    });

  if (error) {
    console.error('Error assigning questionnaire:', error);
    throw error;
  }
}

/**
 * Submit questionnaire responses and update assignment status
 */
export async function submitQuestionnaireResponses(
  data: QuestionnaireResponseFormData
): Promise<void> {
  // Start a transaction-like operation
  // First, insert all responses
  const { error: responsesError } = await supabase
    .from('patient_questionnaire_responses')
    .insert(
      data.responses.map(response => ({
        assignment_id: data.assignmentId,
        question_id: response.questionId,
        response_text: response.responseText,
      }))
    );

  if (responsesError) {
    console.error('Error inserting responses:', responsesError);
    throw responsesError;
  }

  // Then, update the assignment status to completed
  const { error: assignmentError } = await supabase
    .from('patient_questionnaire_assignments')
    .update({
      status: 'completed',
      completed_at: new Date().toISOString(),
    })
    .eq('id', data.assignmentId);

  if (assignmentError) {
    console.error('Error updating assignment status:', assignmentError);
    throw assignmentError;
  }
}

/**
 * Get assignment by ID (useful for getting patient ID for cache invalidation)
 */
export async function getAssignmentById(
  assignmentId: string
): Promise<AssignmentWithQuestionnaire | null> {
  const { data, error } = await supabase
    .from('patient_questionnaire_assignments')
    .select(`
      *,
      questionnaire:questionnaires(
        id,
        title,
        description,
        user_id,
        created_at,
        updated_at,
        questions:questionnaire_questions(
          id,
          question_text,
          question_order,
          questionnaire_id,
          created_at
        )
      )
    `)
    .eq('id', assignmentId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No rows returned
      return null;
    }
    console.error('Error fetching assignment by ID:', error);
    throw error;
  }

  return data as AssignmentWithQuestionnaire;
}

/**
 * Delete a questionnaire assignment (and cascade delete responses)
 */
export async function deleteQuestionnaireAssignment(
  assignmentId: string
): Promise<void> {
  const { error } = await supabase
    .from('patient_questionnaire_assignments')
    .delete()
    .eq('id', assignmentId);

  if (error) {
    console.error('Error deleting questionnaire assignment:', error);
    throw error;
  }
}

/**
 * Update assignment status
 */
export async function updateAssignmentStatus(
  assignmentId: string,
  status: 'pending' | 'completed',
  completedAt?: string
): Promise<void> {
  const updateData: any = { status };
  
  if (status === 'completed' && completedAt) {
    updateData.completed_at = completedAt;
  } else if (status === 'pending') {
    updateData.completed_at = null;
  }

  const { error } = await supabase
    .from('patient_questionnaire_assignments')
    .update(updateData)
    .eq('id', assignmentId);

  if (error) {
    console.error('Error updating assignment status:', error);
    throw error;
  }
}

/**
 * Get questionnaire assignments count by status for a patient
 */
export async function getPatientQuestionnaireStats(
  patientId: string
): Promise<{ pending: number; completed: number; total: number }> {
  const { data, error } = await supabase
    .from('patient_questionnaire_assignments')
    .select('status')
    .eq('patient_id', patientId);

  if (error) {
    console.error('Error fetching questionnaire stats:', error);
    throw error;
  }

  const stats = (data || []).reduce(
    (acc, assignment) => {
      acc.total++;
      if (assignment.status === 'pending') {
        acc.pending++;
      } else if (assignment.status === 'completed') {
        acc.completed++;
      }
      return acc;
    },
    { pending: 0, completed: 0, total: 0 }
  );

  return stats;
}
